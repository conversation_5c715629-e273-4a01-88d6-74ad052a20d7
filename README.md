# React Native Expo Template

This is a React Native project template built using Expo. The template includes essential libraries and boilerplate screens to help you quickly set up an application with authentication, navigation, and state management.

## Features

- **Expo Router**: Enables easy routing and navigation throughout the app.
- **Redux Toolkit**: Simplifies state management and integrates seamlessly with Redux.
- **Redux Persist**: Persists and rehydrates Redux store across app launches.
- **Stack and Tab Navigation**: Includes a pre-configured stack navigator for managing screens and tab bar navigation for primary sections.

## Screens Included

### Auth Screens
- **Register**: New user registration screen.
- **Login**: User login screen.
- **Forgot Password**: Screen to request password reset.
- **Verify OTP**: OTP verification for secure actions.
- **Reset Password**: Password reset screen.

### Main Screens
- **Home Tab**: Primary tab for home screen features.
- **Profile Tab**: Tab for user profile information.
