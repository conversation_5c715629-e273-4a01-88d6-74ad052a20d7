import { COLORS } from '../constants'

export interface IColors {
  primary: string
  text: string
  background: string
  inputBg: string
}

export const lightColors: IColors = {
  primary: COLORS.primary,
  text: COLORS.dark,
  background: COLORS.white,
  inputBg: COLORS.lightWhite,
}

export const darkColors: IColors = {
  primary: COLORS.primary,
  text: COLORS.white,
  background: COLORS.dark,
  inputBg: COLORS.lightBlack,
}
