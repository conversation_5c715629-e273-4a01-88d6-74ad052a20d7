import React, { createContext, useContext, useEffect, useState } from 'react'
import { darkColors, IColors, lightColors } from './colors'
import { Appearance, ColorSchemeName, useColorScheme } from 'react-native'

export const ThemeContext = createContext<{
  dark: boolean
  themeColors: IColors
  setScheme: (scheme: ColorSchemeName) => void
}>({
  dark: false,
  themeColors: lightColors,
  setScheme: () => {},
})

export const ThemeProvider = (props: any) => {
  const colorScheme = useColorScheme()
  const [isDark, setIsDark] = useState(colorScheme === 'dark')

  useEffect(() => {
    const subscription = Appearance.addChangeListener(({ colorScheme }) => {
      setIsDark(colorScheme === 'dark')
    })
    return () => {
      subscription.remove()
    }
  }, [])

  const defaultTheme = {
    dark: isDark,
    themeColors: isDark ? darkColors : lightColors,
    setScheme: (scheme: ColorSchemeName) => setIsDark(scheme === 'dark'),
  }

  return (
    <ThemeContext.Provider value={defaultTheme}>
      {props.children}
    </ThemeContext.Provider>
  )
}

export const useTheme = () => useContext(ThemeContext)
