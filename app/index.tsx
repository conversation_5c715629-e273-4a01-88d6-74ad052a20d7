import { useEffect, useState } from 'react'
import { Redirect } from 'expo-router'
import * as SplashScreen from 'expo-splash-screen'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { PATHS, StorageKey } from '@/constants'
import { getDataFromLocalStorage } from '@/utils'
import React from 'react'

SplashScreen.preventAutoHideAsync()

export default function Index() {
  const [initialRoute, setInitialRoute] = useState<string | null>(null)

  useEffect(() => {
    const checkState = async () => {
      try {
        const onboarded = await getDataFromLocalStorage(
          StorageKey.isOnBoardingDone
        )
        if (!onboarded) {
          setInitialRoute(PATHS.onboarding)
          return
        }

        // Mock auth check
        const loggedIn = await getDataFromLocalStorage(StorageKey.isLoggedIn)
        if (Boolean(loggedIn) === true) {
          setInitialRoute(PATHS.tabs)
        } else {
          setInitialRoute(PATHS.login)
        }
      } finally {
        await SplashScreen.hideAsync()
      }
    }

    checkState()
  }, [])

  if (!initialRoute) return null

  return <Redirect href={initialRoute} />
}
