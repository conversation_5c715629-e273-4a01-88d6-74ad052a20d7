import { Button, Input } from '@/components'
import { PATHS, FONTS, EMAIL_REGEX } from '@/constants'
import { Flex, ScreenContainer } from '@/styles'
import { Ionicons } from '@expo/vector-icons'
import { Link, useRouter } from 'expo-router'

import { Alert, Keyboard, Text, TouchableOpacity } from 'react-native'
import { authStyles } from './register'
import { useTheme } from '@/theme'
import { useState } from 'react'
import { useForgotPasswordMutation } from '@/store/api/auth'
import { Controller, useForm } from 'react-hook-form'
import React from 'react'

const ForgotPassword = () => {
  const { themeColors } = useTheme()
  const router = useRouter()
  const [forgotPassword, { isLoading, data }] = useForgotPasswordMutation()

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm({
    defaultValues: {
      email: '',
    },
    mode: 'onChange',
  })

  const handleResetClick = async ({ email }: { email: string }) => {
    Keyboard.dismiss()
    await forgotPassword({ email })
    await router.replace({
      pathname: PATHS.verifyOtp,
      params: { email, type: 'recovery' },
    })
  }
  return (
    <ScreenContainer style={{ paddingHorizontal: 20 }}>
      <Flex cover gap={40}>
        <Flex gap={20}>
          <Ionicons name='book' size={50} color={themeColors.text} />
          <Text style={[authStyles.authHeading, { color: themeColors.text }]}>
            Forgot Password?
          </Text>
        </Flex>
        <Flex gap={20}>
          <Controller
            name='email'
            control={control}
            rules={{
              required: 'Email is required',
              pattern: {
                value: EMAIL_REGEX,
                message: 'Please enter a valid email!',
              },
            }}
            render={({ field }) => (
              <Input
                inputMode='email'
                iconPosition='right'
                placeholder='Email'
                error={errors?.email?.message}
                icon={
                  <Ionicons name='mail' size={20} color={themeColors.text} />
                }
                {...field}
              />
            )}
          />

          <Button
            onPress={handleSubmit(handleResetClick)}
            isLoading={isLoading}
          >
            Reset Password
          </Button>
        </Flex>

        <Flex direction='row' gap={10} justifyContent='center'>
          <TouchableOpacity>
            <Link href={PATHS.login} style={{ ...FONTS.link }}>
              Back to Login
            </Link>
          </TouchableOpacity>
        </Flex>
      </Flex>
    </ScreenContainer>
  )
}

export default ForgotPassword
