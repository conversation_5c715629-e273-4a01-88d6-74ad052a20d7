import { ILLUSTRATION, PASSWORD_REGEX, PATHS } from '@/constants'
import { Flex, ScreenContainer } from '@/styles'
import React, { useState } from 'react'
import { Image, ScrollView, Text } from 'react-native'
import { authStyles } from './register'
import { useTheme } from '@/theme'
import { Button, CustomModal, Input } from '@/components'
import { useRouter } from 'expo-router'
import { Controller, useForm } from 'react-hook-form'
import { useUpdateProfileMutation } from '@/store/api/profile'
import { useSignOutMutation } from '@/store/api/auth'
import { setIsLoggedIn, useAppDispatch } from '@/store'

const ResetPassword = () => {
  const { themeColors } = useTheme()
  const [showModal, setShowModal] = useState(false)
  const [signOut] = useSignOutMutation()

  const [updateProfile, { isLoading }] = useUpdateProfileMutation()

  const dispatch = useAppDispatch()
  const handleCreatePassword = async (values: IResetPassword) => {
    const { error } = await updateProfile({ password: values?.confirmPassword })

    if (!error) {
      setShowModal(true)
    }
  }
  const { replace } = useRouter()

  interface IResetPassword {
    password: string
    confirmPassword: string
  }

  const {
    control,
    watch,
    handleSubmit,
    formState: { errors },
  } = useForm<IResetPassword>({
    defaultValues: {
      password: '',
      confirmPassword: '',
    },
    mode: 'onChange',
  })

  const password = watch('password')

  return (
    <>
      <ScreenContainer style={{ paddingHorizontal: 20 }}>
        <ScrollView
          contentContainerStyle={{ paddingTop: 80 }}
          showsVerticalScrollIndicator={false}
        >
          <Image
            style={{
              width: '85%',
              height: 200,
              paddingHorizontal: 20,
              marginHorizontal: 'auto',
              marginVertical: 20,
            }}
            source={ILLUSTRATION.password}
            resizeMode='contain'
          />

          <Flex gap={30} style={{ paddingBottom: 40 }}>
            <Text style={[authStyles.authHeading, { color: themeColors.text }]}>
              Create New Password
            </Text>

            <Flex gap={24}>
              <Controller
                control={control}
                name='password'
                rules={{
                  required: 'Password is required',
                  pattern: {
                    value: PASSWORD_REGEX,
                    message:
                      'Password must be at least 8 characters and contain at least one uppercase letter, one lowercase letter, one number, and one special character with no spaces',
                  },
                }}
                render={({ field }) => (
                  <Input
                    placeholder='New Password'
                    isPassword
                    iconPosition='right'
                    {...field}
                    error={errors?.password?.message}
                  />
                )}
              />
              <Controller
                control={control}
                name='confirmPassword'
                rules={{
                  required: 'Please confirm your password',
                  validate: value =>
                    value === password || 'Passwords do not match',
                }}
                render={({ field }) => (
                  <Input
                    placeholder='Confirm New Password'
                    isPassword
                    iconPosition='right'
                    {...field}
                    error={errors?.confirmPassword?.message}
                  />
                )}
              />
            </Flex>

            <Button
              onPress={handleSubmit(handleCreatePassword)}
              isLoading={isLoading}
            >
              Create New Password
            </Button>
          </Flex>
        </ScrollView>
      </ScreenContainer>

      <CustomModal
        showModal={showModal}
        setShowModal={setShowModal}
        onBtnPress={async () => {
          const { error } = await signOut()
          if (!error) {
            dispatch(setIsLoggedIn(false))
            setShowModal(false)
            replace(PATHS.login)
          }
        }}
        isInfoModal
        modalData={{
          title: 'Password Updated',
          description:
            'Your password has been updated successfully. Please login',
        }}
      />
    </>
  )
}

export default ResetPassword
