import React, { useState } from 'react'
import { Keyboard, StyleSheet, Text } from 'react-native'
import { Link, Redirect, router } from 'expo-router'
import Ionicons from '@expo/vector-icons/Ionicons'
import { Controller, useForm } from 'react-hook-form'

import { Flex, ScreenContainer } from '../../styles'
import { EMAIL_REGEX, FONTS, PASSWORD_REGEX, PATHS } from '@/constants'
import { Button, Input } from '@/components'
import { useTheme } from '@/theme'
import { setIsLoggedIn, useAppDispatch, useAppSelector } from '@/store'
import { clearLocalStorage } from '@/utils'
import { useCreateUserMutation } from '@/store/api/auth'
import { supabase } from '@/lib'

interface IRegisterProps {
  email: string
  password: string
  confirmPassword?: string
}

const Register = () => {
  const { themeColors } = useTheme()
  const [createUser, { isLoading }] = useCreateUserMutation()

  const {
    control,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<IRegisterProps>({
    defaultValues: {
      email: '',
      password: '',
      confirmPassword: '',
    },
    mode: 'onChange',
  })

  const onSubmit = async (data: IRegisterProps) => {
    Keyboard.dismiss()
    const response = await createUser({
      email: data?.email,
      password: data.password,
    })

    if (response?.data?.user?.email) {
      router.replace({
        pathname: PATHS.verifyOtp,
        params: { email: data?.email, type: 'signup' },
      })
      // dispatch(setIsLoggedIn(true))
    }
  }

  const password = watch('password')

  return (
    <ScreenContainer style={{ paddingHorizontal: 20 }}>
      <Flex cover gap={40}>
        <Flex gap={20}>
          <Ionicons name='book' size={50} color={themeColors.text} />
          <Text style={[authStyles.authHeading, { color: themeColors.text }]}>
            Create Your Account
          </Text>
        </Flex>

        <Flex gap={20}>
          <Controller
            control={control}
            name='email'
            rules={{
              required: 'Email is required',
              pattern: {
                value: EMAIL_REGEX,
                message: 'Please enter a valid email!',
              },
            }}
            render={({ field }) => (
              <Input
                placeholder='Email'
                iconPosition='right'
                {...field}
                textContentType='emailAddress'
                error={errors?.email?.message}
                icon={
                  <Ionicons name='mail' size={20} color={themeColors.text} />
                }
              />
            )}
          />

          <Controller
            control={control}
            name='password'
            rules={{
              required: 'Password is required',
              pattern: {
                value: PASSWORD_REGEX,
                message:
                  'Password must be at least 8 characters and contain at least one uppercase letter, one lowercase letter, one number, and one special character with no spaces',
              },
            }}
            render={({ field }) => (
              <Input
                iconPosition='right'
                placeholder='Password'
                isPassword
                {...field}
                error={errors?.password?.message}
              />
            )}
          />
          <Controller
            control={control}
            name='confirmPassword'
            rules={{
              required: 'Please confirm your password',
              validate: value => value === password || 'Passwords do not match',
            }}
            render={({ field }) => (
              <Input
                iconPosition='right'
                placeholder='Confirm password'
                isPassword
                {...field}
                error={errors?.confirmPassword?.message}
              />
            )}
          />

          <Button isLoading={isLoading} onPress={handleSubmit(onSubmit)}>
            Create Account
          </Button>

          <Flex gap={40}>
            <Flex gap={10} direction='row'>
              <Text style={{ fontSize: 16, color: themeColors.text }}>
                Already have an account?
              </Text>
              <Link href={PATHS.login} style={{ ...FONTS.link }}>
                Sign In
              </Link>
            </Flex>
          </Flex>
        </Flex>
      </Flex>
    </ScreenContainer>
  )
}

export const authStyles = StyleSheet.create({
  authHeading: {
    ...FONTS.h2,
  },
})

export default Register
