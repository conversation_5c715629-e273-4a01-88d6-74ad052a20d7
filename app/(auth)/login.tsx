import { Link, useFocusEffect } from 'expo-router'
import { Ionicons } from '@expo/vector-icons'
import { <PERSON><PERSON>, BackHandler, Keyboard, Platform, Text } from 'react-native'
import { Controller, useForm } from 'react-hook-form'

import { EMAIL_REGEX, FONTS, PATHS } from '@/constants'
import { Flex, ScreenContainer } from '@/styles'
import { authStyles } from './register'
import { Button, Input } from '@/components'
import { useTheme } from '@/theme'
import { useLoginUserMutation } from '@/store/api'
import { GooglePlacesAutocomplete } from 'react-native-google-places-autocomplete'
import React, { useCallback } from 'react'

interface ILoginProps {
  email: string
  password: string
}

const Login = () => {
  const { themeColors } = useTheme()
  const [loginUser, { isLoading }] = useLoginUserMutation()

  const handleLogin = async ({ email, password }: ILoginProps) => {
    Keyboard.dismiss()
    await loginUser({ email, password })
  }

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<ILoginProps>({
    defaultValues: {
      email: '',
      password: '',
    },
    mode: 'onChange',
  })

  useFocusEffect(
    useCallback(() => {
      const onBackPress = () => {
        Alert.alert('Exit App', 'Are you sure you want to exit?', [
          {
            text: 'Cancel',
            onPress: () => null,
            style: 'cancel',
          },
          { text: 'YES', onPress: () => BackHandler.exitApp() },
        ])
        return true
      }

      if (Platform.OS === 'android') {
        BackHandler.addEventListener('hardwareBackPress', onBackPress)
      }

      return () => {
        if (Platform.OS === 'android') {
          BackHandler.removeEventListener('hardwareBackPress', onBackPress)
        }
      }
    }, [])
  )

  return (
    <ScreenContainer style={{ paddingHorizontal: 20 }}>
      <Flex cover gap={40}>
        <Flex gap={20}>
          <Ionicons name='book' size={50} color={themeColors.text} />

          <Text style={[authStyles.authHeading, { color: themeColors.text }]}>
            Login to Your Account
          </Text>
        </Flex>

        <Flex gap={20}>
          <Controller
            control={control}
            name='email'
            rules={{
              required: 'Email is required',
              pattern: {
                value: EMAIL_REGEX,
                message: 'Please enter a valid email!',
              },
            }}
            render={({ field }) => (
              <Input
                error={errors?.email?.message}
                iconPosition='right'
                placeholder='Email'
                textContentType='emailAddress'
                icon={
                  <Ionicons name='mail' size={20} color={themeColors.text} />
                }
                {...field}
              />
            )}
          />

          <Controller
            control={control}
            name='password'
            rules={{
              required: 'Password is required',
            }}
            render={({ field }) => (
              <Input
                iconPosition='right'
                placeholder='Password'
                isPassword
                {...field}
                error={errors?.password?.message}
              />
            )}
          />

          <Button isLoading={isLoading} onPress={handleSubmit(handleLogin)}>
            Login
          </Button>
        </Flex>

        <Flex direction='row' gap={10} justifyContent='space-between'>
          <Link href={PATHS.register} style={{ ...FONTS.link }}>
            Create Account
          </Link>
          <Link href={PATHS.forgotPassword} style={{ ...FONTS.link }}>
            Forgot Password?
          </Link>
        </Flex>
      </Flex>
    </ScreenContainer>
  )
}

export default Login
