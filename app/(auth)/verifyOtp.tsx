import { useLocalSearchParams, useRouter } from 'expo-router'
import { OtpInput } from 'react-native-otp-entry'
import { Image, Keyboard, ScrollView, Text } from 'react-native'

import { FONTS, ILLUSTRATION, PATHS } from '@/constants'
import { Flex, ScreenContainer } from '@/styles'

import { authStyles } from './register'
import { useTheme } from '@/theme'
import { Button } from '@/components'
import { useVerifyOtpMutation } from '@/store/api/auth'
import { useState } from 'react'
import { EmailOtpType } from '@supabase/supabase-js'

const VerifyOtp = () => {
  const { themeColors } = useTheme()
  const navState = useLocalSearchParams()
  const { replace } = useRouter()
  const [otp, setOtp] = useState('')
  const [verifyOtp, { isLoading }] = useVerifyOtpMutation()

  const handleVerifyPress = async () => {
    await verifyOtp({
      email: navState?.email! as string,
      otp,
      type: navState?.type! as EmailOtpType,
    })
  }

  return (
    <ScreenContainer style={{ paddingHorizontal: 20 }}>
      <ScrollView
        contentContainerStyle={{
          paddingTop: 80,
        }}
        showsVerticalScrollIndicator={false}
      >
        <Image
          style={{
            width: '85%',
            height: 200,
            paddingHorizontal: 20,
            marginHorizontal: 'auto',
            marginVertical: 20,
          }}
          source={ILLUSTRATION.mail}
          resizeMode='contain'
        />

        <Flex gap={30}>
          <Text style={[authStyles.authHeading, { color: themeColors.text }]}>
            Verify OTP
          </Text>

          <Flex gap={10}>
            <Text style={{ ...FONTS.bodyRegular, color: themeColors.text }}>
              OTP has been sent to your email address
            </Text>
            <Text style={{ ...FONTS.h3, color: themeColors.text }}>
              {navState?.email}
            </Text>
          </Flex>

          <Flex gap={40} style={{ paddingBottom: 40 }}>
            <OtpInput
              theme={{
                pinCodeContainerStyle: {
                  width: 50,
                },
                pinCodeTextStyle: {
                  ...FONTS.h2,
                  color: themeColors.text,
                },
              }}
              numberOfDigits={6}
              type='numeric'
              onFilled={code => {
                setOtp(code)
                Keyboard.dismiss()
              }}
              onTextChange={text => console.log(text)}
            />

            <Button onPress={handleVerifyPress} isLoading={isLoading}>
              Verify
            </Button>
          </Flex>
        </Flex>
      </ScrollView>
    </ScreenContainer>
  )
}

export default VerifyOtp
