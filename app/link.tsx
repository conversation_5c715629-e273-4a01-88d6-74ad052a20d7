import { View, Text, StyleSheet, TouchableOpacity, Switch } from 'react-native'

import { Flex, ScreenContainer } from '@/styles'
import WebView from 'react-native-webview'
import { FloatingAction } from 'react-native-floating-action'
import { Ionicons } from '@expo/vector-icons'
import { getBlogTitle, saveDataToLocalStorage } from '@/utils'
import { COLORS, getPlatFormConstants, PATHS, StorageKey } from '@/constants'
import { router, Stack, useLocalSearchParams } from 'expo-router'
import {
  useCreateBookmarkMutation,
  useDeleteBookmarkMutation,
  useUpdateBookmarkMutation,
} from '@/store/api/bookmarks'
import { useTheme } from '@/theme'
import { Button, CustomModal, Input, Tag } from '@/components'
import { useRef, useState } from 'react'
import { MultiSelect } from 'react-native-element-dropdown'
import { useGetAllTagsQuery } from '@/store/api/tags'
import { rgba } from 'polished'
import React from 'react'
import { SafeAreaView } from 'react-native-safe-area-context'

const actions = [
  {
    text: 'Save',
    icon: <Ionicons name='save' size={20} color={'white'} />,
    name: 'save',
    position: 1,
  },
]

const Link = () => {
  const data = useLocalSearchParams() as Record<string, any>
  const { themeColors } = useTheme()
  const [selectedTags, setSelectedTags] = useState<string[]>(
    data?.tags?.split(',') || []
  )

  const medium = data?.mediumUrl === 'true'

  const { data: tagsData } = useGetAllTagsQuery()
  const isFromBookmark = !!data?.fromPath

  const [showModal, setShowModal] = useState(false)
  const [bookmarkTitle, setBookmarkTitle] = useState(data?.title || '')
  const [createBookmark, { isLoading }] = useCreateBookmarkMutation()
  const [deleteBookmark, { isLoading: deleteLoading }] =
    useDeleteBookmarkMutation()
  const [updateBookmark, { isLoading: updateLoading }] =
    useUpdateBookmarkMutation()

  const [isMedium, setIsMedium] = useState(medium || false)
  const toggleSwitch = () => setIsMedium(previousState => !previousState)

  const tagOptions = tagsData?.map((tag: { title: string }) => ({
    label: tag?.title,
    value: tag?.title,
  }))

  const blogUrl =
    medium || !isFromBookmark ? `https://freedium.cfd/${data?.url}` : data?.url

  const handleBtnClick = (name: string) => {
    switch (name) {
      case 'save':
        setShowModal(true)

        break
    }
  }

  const renderItem = (item: { value: string; label: string }) => {
    return (
      <View
        style={[
          styles.item,
          { backgroundColor: rgba(themeColors.inputBg, 0.8) },
        ]}
      >
        <Text
          style={[
            styles.selectedTextStyle,
            {
              color: themeColors.text,
            },
          ]}
        >
          {item.label}
        </Text>

        {selectedTags?.includes(item?.value) ? (
          <Ionicons name='checkmark' size={18} color={themeColors.text} />
        ) : null}
      </View>
    )
  }

  const handleDeleteClick = async () => {
    const { error } = await deleteBookmark({ id: data?.id })

    if (!error) {
      setShowModal(false)
      router.replace(PATHS.bookmarks)
    }
  }
  const handleUpdateClick = async () => {
    const { error } = await updateBookmark({
      id: data?.id,
      data: {
        title: bookmarkTitle,
        tags: selectedTags,
        isMedium,
      },
    })

    if (!error) {
      setShowModal(false)
      router.replace(PATHS.bookmarks)
    }
  }

  const handleAddClick = async () => {
    const { error } = await createBookmark({
      title: getBlogTitle(data?.url! as string),
      url: blogUrl,
      createdBy: data?.userId! as string,
      tags: selectedTags!,
    })

    if (!error) {
      setShowModal(false)
    }
  }

  const blogTitle = isFromBookmark
    ? data?.title
    : getBlogTitle(data?.url! as string)

  return (
    <>
      {getPlatFormConstants().isWeb ? (
        <iframe
          src={blogUrl}
          style={{ width: '100%', height: '100%', border: 'none' }}
          title='Web Viewer'
        />
      ) : (
        <WebView source={{ uri: blogUrl }} />
      )}

      <Stack.Screen
        options={{
          headerRight: () => (
            <Ionicons
              name='pencil'
              size={20}
              color={themeColors?.text}
              onPress={() => setShowModal(true)}
            />
          ),
        }}
      />

      {isFromBookmark ? null : (
        <FloatingAction
          actions={actions}
          onPressItem={name => {
            handleBtnClick(name!)
          }}
        />
      )}

      <CustomModal showModal={showModal} setShowModal={setShowModal}>
        <Flex
          gap={16}
          style={{
            paddingHorizontal: 16,
            paddingVertical: 30,
            borderRadius: 8,
            backgroundColor: themeColors.background,
            width: '97%',
          }}
        >
          {isFromBookmark ? (
            <Input
              placeholder='title'
              defaultValue={blogTitle}
              label='Title'
              value={bookmarkTitle}
              onChange={setBookmarkTitle}
            />
          ) : null}

          <Flex
            direction='row'
            gap={16}
            justifyContent='flex-start'
            style={{ marginRight: 'auto' }}
          >
            <Switch
              trackColor={{ false: '#767577', true: COLORS.primary }}
              thumbColor={'#f4f3f4'}
              ios_backgroundColor='#3e3e3e'
              onValueChange={toggleSwitch}
              value={isMedium}
            />
            <Text style={{ color: themeColors.text }}>Medium Website?</Text>
          </Flex>

          <View
            style={{
              width: '100%',
            }}
          >
            <MultiSelect
              style={[
                styles.dropdown,
                { backgroundColor: themeColors.inputBg },
              ]}
              placeholderStyle={[
                styles.placeholderStyle,
                { color: themeColors.text },
              ]}
              selectedTextStyle={styles.selectedTextStyle}
              inputSearchStyle={styles.inputSearchStyle}
              iconStyle={styles.iconStyle}
              data={tagOptions}
              labelField='label'
              valueField='value'
              placeholder='Select Tags'
              value={selectedTags}
              search
              searchPlaceholder='Search Tags..'
              onChange={setSelectedTags}
              containerStyle={{
                backgroundColor: themeColors.inputBg,
                marginTop: -30,
                borderWidth: 0,
              }}
              renderItem={renderItem}
              renderSelectedItem={(item, unSelect) => (
                <Tag
                  title={item?.label}
                  onPress={() => unSelect && unSelect(item)}
                  showIcon
                />
              )}
            />
            <Text
              style={{
                color: 'transparent',
              }}
            >
              Lorem ipsum dolor sit, amet consectetur adipisicing elit. Eaque
            </Text>
          </View>
          <Flex
            direction='row'
            gap={8}
            style={{ width: '100%', marginTop: 30 }}
          >
            {isFromBookmark ? (
              <>
                <Button
                  style={{ flex: 1 }}
                  onPress={handleUpdateClick}
                  isLoading={updateLoading}
                >
                  Update
                </Button>
                <Button
                  style={{ flex: 1 }}
                  variant='destructive'
                  isLoading={deleteLoading}
                  onPress={handleDeleteClick}
                >
                  Delete
                </Button>
              </>
            ) : (
              <Button onPress={handleAddClick} isLoading={isLoading}>
                Add
              </Button>
            )}
          </Flex>
        </Flex>
      </CustomModal>
    </>
  )
}

export default Link

export const styles = StyleSheet.create({
  dropdown: {
    height: 50,
    borderRadius: 8,
    padding: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
    elevation: 2,
  },
  placeholderStyle: {
    fontSize: 16,
  },
  selectedTextStyle: {
    fontSize: 14,
  },
  iconStyle: {
    width: 20,
    height: 20,
  },
  inputSearchStyle: {
    height: 40,
    fontSize: 16,
    borderWidth: 0,
  },
  icon: {
    marginRight: 5,
  },
  item: {
    padding: 17,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
})
