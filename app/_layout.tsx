import React from 'react'
import { Stack } from 'expo-router'
import { StatusBar } from 'expo-status-bar'

import { ThemeProvider } from '../theme'
import { SafeAreaProvider } from 'react-native-safe-area-context'
import { COLORS, PATHS } from '../constants'
import { Provider } from 'react-redux'
import { useColorScheme, View } from 'react-native'
import AuthProvider from '@/lib/AuthProvider'
import Toast, { ErrorToast, SuccessToast } from 'react-native-toast-message'
import { store } from '@/store'

const App = () => {
  const isDarkMode = useColorScheme() === 'dark'

  return (
    <ThemeProvider>
      <Provider store={store}>
        <AuthProvider>
          <SafeAreaProvider>
            <View
              style={{
                flex: 1,
                backgroundColor: isDarkMode ? COLORS.dark : COLORS.white,
              }}
            >
              <StatusBar
                style={isDarkMode ? 'light' : 'dark'}
                backgroundColor={isDarkMode ? COLORS.dark : COLORS.white}
              />
              <Stack
                screenOptions={{
                  contentStyle: {
                    backgroundColor: isDarkMode ? COLORS.dark : COLORS.white,
                  },
                }}
              >
                <Stack.Screen
                  name={PATHS.tabs}
                  options={{
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name='index'
                  options={{
                    presentation: 'modal',
                    headerShown: false,
                    animation: 'ios',
                  }}
                />
                <Stack.Screen
                  name={PATHS.register}
                  options={{ animation: 'ios', headerShown: false }}
                />
                <Stack.Screen
                  name={PATHS.login}
                  options={{ animation: 'ios', headerShown: false }}
                />
                <Stack.Screen
                  name={PATHS.forgotPassword}
                  options={{
                    animation: 'ios',
                    headerShown: false,
                    contentStyle: {
                      backgroundColor: isDarkMode ? COLORS.dark : COLORS.white,
                    },
                  }}
                />
                <Stack.Screen
                  name={PATHS.verifyOtp}
                  options={{
                    animation: 'ios',
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name={PATHS.resetPassword}
                  options={{
                    animation: 'ios',
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name={PATHS.onboarding}
                  options={{
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name={PATHS.link}
                  options={{
                    headerStyle: {
                      backgroundColor: isDarkMode ? COLORS.dark : COLORS.white,
                    },
                    headerTitleStyle: {
                      color: isDarkMode ? COLORS.white : COLORS.dark,
                    },
                    headerTintColor: isDarkMode ? COLORS.white : COLORS.dark,
                    animation: 'ios',
                    title: 'Page detail',
                    headerBackTitleVisible: false,
                  }}
                />
                <Stack.Screen
                  name={PATHS.tags}
                  options={{
                    headerStyle: {
                      backgroundColor: isDarkMode ? COLORS.dark : COLORS.white,
                    },
                    headerTitleStyle: {
                      color: isDarkMode ? COLORS.white : COLORS.dark,
                    },
                    headerTintColor: isDarkMode ? COLORS.white : COLORS.dark,
                    animation: 'ios',
                    title: 'Tags',
                    headerBackTitleVisible: false,
                  }}
                />
                <Stack.Screen
                  name={PATHS.websites}
                  options={{
                    headerStyle: {
                      backgroundColor: isDarkMode ? COLORS.dark : COLORS.white,
                    },
                    headerTitleStyle: {
                      color: isDarkMode ? COLORS.white : COLORS.dark,
                    },
                    headerTintColor: isDarkMode ? COLORS.white : COLORS.dark,
                    animation: 'ios',
                    title: 'Websites',
                    headerBackTitleVisible: false,
                  }}
                />
              </Stack>
            </View>
          </SafeAreaProvider>
          <Toast
            topOffset={56}
            config={{
              success: props => (
                <SuccessToast
                  {...props}
                  text1Style={{
                    fontSize: 16,
                  }}
                  text2Style={{
                    fontSize: 14,
                  }}
                />
              ),
              error: props => (
                <ErrorToast
                  {...props}
                  text1Style={{
                    fontSize: 16,
                  }}
                  text2Style={{
                    fontSize: 14,
                  }}
                />
              ),
            }}
          />
        </AuthProvider>
      </Provider>
    </ThemeProvider>
  )
}

export default App
