import { NoDataUI } from '../components/NoData'
import { ScrollView, Alert, Keyboard, KeyboardAvoidingView } from 'react-native'
import React, { useState } from 'react'
import { Stack } from 'expo-router'
import { Flex } from '@/styles'
import { Ionicons } from '@expo/vector-icons'
import { useTheme } from '@/theme'
import { Button, CustomModal, IconButton, Input, Tag } from '@/components'
import {
  useCreateTagMutation,
  useDeleteTagMutation,
  useGetAllTagsQuery,
  useUpdateTagMutation,
} from '@/store/api/tags'
import { useAuth } from '@/lib/AuthProvider'
import { showConfirmationDialog } from '@/utils'

const Tags = () => {
  const { themeColors } = useTheme()
  const [tagTitle, setTagTitle] = useState('')
  const [showModal, setShowModal] = useState(false)
  const { data: tagsData, isLoading } = useGetAllTagsQuery()
  const { user } = useAuth()
  const [selectedTag, setSelectedTag] = useState<any>({})
  const [createTag, { isLoading: createTagLoading }] = useCreateTagMutation()
  const [deleteTag, { isLoading: deleteTagLoading }] = useDeleteTagMutation()
  const [updateTag, { isLoading: updateTagLoading }] = useUpdateTagMutation()

  const handleCreateTag = async () => {
    if (!tagTitle) {
      showConfirmationDialog('Please enter tag name')
      return
    }
    Keyboard.dismiss()
    const { error } = await createTag({
      createdBy: user?.id!,
      title: tagTitle,
    })

    if (!error) {
      setTagTitle('')
      setShowModal(false)
    }
  }

  const handleDeleteTag = async () => {
    const { error } = await deleteTag({
      tagId: selectedTag?.id,
    })

    if (!error) {
      setSelectedTag({})
      setShowModal(false)
    }
  }

  const handleUpdateTag = async () => {
    if (!tagTitle) {
      showConfirmationDialog('Please enter tag name')

      return
    }
    Keyboard.dismiss()
    const { error } = await updateTag({
      tagId: selectedTag?.id,
      title: tagTitle,
    })

    if (!error) {
      setSelectedTag({})
      setShowModal(false)
    }
  }

  if (isLoading) {
    return null
  }

  return (
    <>
      <Stack.Screen
        options={{
          headerRight: () => (
            <IconButton
              icon={
                <Ionicons name='add-sharp' size={24} color={themeColors.text} />
              }
              onPress={() => {
                setSelectedTag({})
                setShowModal(true)
              }}
            />
          ),
        }}
      />

      {!tagsData?.length ? (
        <Flex cover style={{ backgroundColor: themeColors.background }}>
          <NoDataUI text='No tags found' />
        </Flex>
      ) : (
        <ScrollView
          style={{
            flex: 1,
            backgroundColor: themeColors.background,
          }}
        >
          <Flex
            direction='row'
            style={{
              flex: 1,
              flexWrap: 'wrap',
              paddingVertical: 10,
              paddingHorizontal: 16,
            }}
            justifyContent='flex-start'
            gap={10}
          >
            {tagsData?.map((itm: any) => (
              <Tag
                title={itm?.title}
                key={itm?.id}
                onPress={() => {
                  setSelectedTag(itm)
                  setShowModal(true)
                }}
              />
            ))}
          </Flex>
        </ScrollView>
      )}

      <CustomModal showModal={showModal} setShowModal={setShowModal}>
        <Flex
          gap={32}
          style={{
            paddingHorizontal: 16,
            paddingVertical: 30,
            borderRadius: 8,
            // width: '100%',

            backgroundColor: themeColors.background,
          }}
        >
          <Input
            placeholder='Enter tag name'
            label='Title'
            defaultValue={selectedTag?.title || tagTitle}
            onChange={setTagTitle}
          />

          {selectedTag?.title ? (
            <Flex direction='row' gap={20}>
              <Button
                style={{ flex: 1 }}
                isLoading={updateTagLoading}
                onPress={handleUpdateTag}
              >
                Update
              </Button>
              <Button
                style={{ flex: 1 }}
                isLoading={deleteTagLoading}
                onPress={handleDeleteTag}
                variant='destructive'
              >
                Delete
              </Button>
            </Flex>
          ) : (
            <Button
              style={{ minWidth: '100%' }}
              isLoading={createTagLoading}
              onPress={handleCreateTag}
            >
              Save
            </Button>
          )}
        </Flex>
      </CustomModal>
    </>
  )
}

export default Tags
