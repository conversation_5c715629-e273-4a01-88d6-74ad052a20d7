import { useEffect, useRef, useState } from 'react'
import { Image, StyleSheet, Text, View } from 'react-native'
import { Redirect, Stack } from 'expo-router'

import { Flex, ScreenContainer } from '@/styles'
import { COLORS, FONTS, ILLUSTRATION, PATHS, StorageKey } from '@/constants'
import { getDataFromLocalStorage, saveDataToLocalStorage } from '@/utils'
import { setOnBoardingState, useAppDispatch, useAppSelector } from '@/store'
import { Button } from '@/components'
import React from 'react'
import { useTheme } from '@/theme'

type ContentType = Record<
  number,
  {
    heading: string
    description: string
  }
>

const onboardingContent: ContentType = {
  0: {
    heading: 'Welcome to Readium',
    description: 'Access exclusive blog content from top writers.',
  },
  1: {
    heading: 'Key Features',
    description:
      'Ad-free, offline reading, and a curated experience just for you.',
  },
  2: {
    heading: 'Get Started',
    description: 'Sign Up now for unlimited access to premium articles!',
  },
}

export default function Onboarding() {
  const { themeColors } = useTheme()
  const [currentIndex, setCurrentIndex] = useState(0)
  const timeOutRef = useRef<any>()
  const dispatch = useAppDispatch()
  const { isOnBoardingDone } = useAppSelector(state => state.ui)

  useEffect(() => {
    ;(async () => {
      const value = await getDataFromLocalStorage(StorageKey.isOnBoardingDone)
      dispatch(setOnBoardingState(value))
    })()

    timeOutRef.current = setInterval(() => {
      handlePress()
    }, 4000)

    return () => {
      clearInterval(timeOutRef.current)
    }
  }, [])

  // clearLocalStorage()
  const handlePress = (stopInterval?: boolean) => {
    stopInterval && clearInterval(timeOutRef.current)
    setCurrentIndex(prev => {
      if (prev === 2) {
        return 0
      }
      return prev + 1
    })
  }

  const handleSkipPress = async () => {
    await saveDataToLocalStorage(StorageKey.isOnBoardingDone, true)
    dispatch(setOnBoardingState(true))
  }

  const getOnBoadingImage = () => {
    switch (currentIndex) {
      case 0:
        return ILLUSTRATION.blog

      case 1:
        return ILLUSTRATION.save

      case 2:
        return ILLUSTRATION.publish
    }
  }

  if (isOnBoardingDone) {
    return <Redirect href={PATHS.login} />
  }

  return (
    <ScreenContainer
      style={{
        paddingHorizontal: 10,
        flex: 1,
        backgroundColor: themeColors.background || COLORS.white,
      }}
    >
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />
      <Flex cover>
        <Image
          style={{ width: '85%', height: 300, paddingHorizontal: 20 }}
          source={getOnBoadingImage()}
          resizeMode='contain'
        />

        <Flex direction='row' gap={10}>
          <View style={[styles.dot, currentIndex === 0 && styles.activeDot]} />
          <View style={[styles.dot, currentIndex === 1 && styles.activeDot]} />
          <View style={[styles.dot, currentIndex === 2 && styles.activeDot]} />
        </Flex>

        <Flex style={{ marginTop: 40 }} gap={20}>
          <Text style={[styles.heading, { color: themeColors.text }]}>
            {onboardingContent[currentIndex].heading}
          </Text>
          <Text style={[styles.subHeading, { color: themeColors.text }]}>
            {onboardingContent[currentIndex].description}
          </Text>
        </Flex>
      </Flex>
      <Flex gap={32} style={{ paddingBottom: 50 }}>
        <Button onPress={() => handlePress(true)}>Next</Button>
        <Button variant='outline' onPress={handleSkipPress}>
          Skip
        </Button>
      </Flex>
    </ScreenContainer>
  )
}

const styles = StyleSheet.create({
  activeDot: {
    backgroundColor: COLORS.primary,
  },
  dot: {
    borderColor: COLORS.primary,
    borderWidth: 1,
    width: 10,
    height: 10,
    borderRadius: 50,
  },
  heading: {
    ...FONTS.h2,
    textAlign: 'center',
  },
  subHeading: {
    ...FONTS.bodyRegular,
    textAlign: 'center',
    letterSpacing: 1.2,
  },
})
