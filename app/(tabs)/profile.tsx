import { Avatar, IconButton, MenuItem } from '@/components'
import { FONTS, getPlatFormConstants, ICONS, PATHS } from '@/constants'
import { useAuth } from '@/lib/AuthProvider'
import { setIsLoggedIn, useAppDispatch } from '@/store'
import { useSignOutMutation } from '@/store/api/auth'
import { useGetProfileQuery } from '@/store/api/profile'
import { Flex, ScreenContainer } from '@/styles'
import { useTheme } from '@/theme'
import { showConfirmationDialog } from '@/utils'
import { Ionicons } from '@expo/vector-icons'
import { router } from 'expo-router'
import React, { useCallback } from 'react'
import { useFocusEffect } from '@react-navigation/native'

import {
  Alert,
  BackHandler,
  Platform,
  RefreshControl,
  ScrollView,
  StyleSheet,
  Text,
  View,
} from 'react-native'

const Profile = () => {
  const dispatch = useAppDispatch()
  const { user } = useAuth()
  const { themeColors } = useTheme()
  const [signOut] = useSignOutMutation()
  const { data, isFetching, refetch } = useGetProfileQuery()

  const handleLogout = async () => {
    try {
      showConfirmationDialog('Are you sure you want to logout?', async () => {
        const { error } = await signOut()
        if (!error) {
          dispatch(setIsLoggedIn(false))
          router.replace(PATHS.login)
        }
      })
    } catch (error) {
      console.log('error===[log]===>', error)
    }
  }

  useFocusEffect(
    useCallback(() => {
      const onBackPress = () => {
        Alert.alert('Exit App', 'Are you sure you want to exit?', [
          {
            text: 'Cancel',
            onPress: () => null,
            style: 'cancel',
          },
          { text: 'YES', onPress: () => BackHandler.exitApp() },
        ])
        return true
      }

      if (Platform.OS === 'android') {
        BackHandler.addEventListener('hardwareBackPress', onBackPress)
      }

      return () => {
        if (Platform.OS === 'android') {
          BackHandler.removeEventListener('hardwareBackPress', onBackPress)
        }
      }
    }, [])
  )

  const menuItems = [
    {
      iconName: 'newspaper',
      title: 'Websites',
      onPress: () => {
        router.push(PATHS.websites)
      },
    },
    {
      iconName: 'pricetags',
      title: 'Tags',
      onPress: () => {
        router.push(PATHS.tags)
      },
    },
    {
      iconName: 'build',
      title: 'Change Password',
      onPress: () => {
        router.push(PATHS.resetPassword)
      },
    },
    {
      iconName: 'exit-outline',
      title: 'Logout',
      onPress: handleLogout,
      renderRight: () => <View />,
    },
  ]

  return (
    <ScreenContainer>
      <ScrollView
        refreshControl={
          <RefreshControl
            refreshing={isFetching}
            onRefresh={() => {
              refetch()
            }}
          />
        }
      >
        <Flex gap={10}>
          {data?.user_metadata?.avatar_url ? (
            <Avatar source={data?.user_metadata?.avatar_url} />
          ) : null}
          <Text style={[styles.emailText, { color: themeColors.text }]}>
            {user?.email}
          </Text>
        </Flex>

        <Flex style={{ marginTop: 20, marginBottom: 20 }}>
          {menuItems?.map((item, index) => (
            <MenuItem
              key={index}
              iconName={item.iconName}
              title={item.title}
              onPress={item.onPress}
              isLogOut={item?.title === 'Logout'}
              renderRightContent={item?.renderRight}
            />
          ))}
        </Flex>
      </ScrollView>
    </ScreenContainer>
  )
}

export default Profile

const styles = StyleSheet.create({
  emailText: {
    ...FONTS.h3,
  },
})
