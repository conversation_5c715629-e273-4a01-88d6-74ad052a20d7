import { router } from 'expo-router'
import React, { useState } from 'react'
import { FlatList, RefreshControl, StatusBar, Text, View } from 'react-native'

import { BlogCard, IconButton, NoDataUI, Tag } from '@/components'
import { FONTS, getPlatFormConstants, PATHS } from '@/constants'
import { useGetAllBookmarksQuery } from '@/store/api/bookmarks'
import { useTheme } from '@/theme'
import { useGetAllTagsQuery } from '@/store/api'

const Bookmarks = () => {
  const { data, refetch, isLoading } = useGetAllBookmarksQuery()
  const { data: tagsData } = useGetAllTagsQuery()
  const [selectedFilter, setSelectedFilter] = useState<string[]>([])
  const { themeColors, dark } = useTheme()

  const handleFilterClick = (filter: string) => {
    setSelectedFilter([filter])
  }

  if (isLoading) return null
  return (
    <>
      <StatusBar
        barStyle={dark ? 'light-content' : 'dark-content'}
        backgroundColor={themeColors.background}
      />
      <FlatList
        data={
          selectedFilter.length > 0
            ? data?.filter(
                (item: any) =>
                  item.tags &&
                  selectedFilter.some(filter => item.tags.includes(filter))
              )
            : data
        }
        ListHeaderComponent={
          <FlatList
            data={[
              {
                created_at: '2024-12-14T12:37:23.064135+00:00',
                created_by: '8775cfc1-fe1b-490c-a4f5-fc911bd01ff9',
                id: 111122,
                title: 'All',
              },
              ...tagsData,
            ]}
            horizontal
            contentContainerStyle={{
              display: 'flex',
              flexDirection: 'row',
              gap: 8,
              alignItems: 'center',
              paddingHorizontal: 10,
              paddingTop: getPlatFormConstants()?.statusbarHeight + 24,
              paddingBottom: 16,
              backgroundColor: themeColors.background,
            }}
            showsHorizontalScrollIndicator={false}
            renderItem={({ item }) => (
              <IconButton
                style={{
                  backgroundColor:
                    selectedFilter?.includes(item?.title) ||
                    (!selectedFilter?.length && item?.title === 'All')
                      ? themeColors.text
                      : themeColors.inputBg,
                  padding: 0,

                  borderRadius: 8,
                }}
                onPress={() => {
                  if (item?.title === 'All') {
                    setSelectedFilter([])
                  } else {
                    handleFilterClick(item?.title)
                  }
                }}
                icon={
                  <Text
                    style={{
                      ...FONTS.medium,
                      fontSize: 14,

                      color:
                        selectedFilter?.includes(item?.title) ||
                        (!selectedFilter?.length && item?.title === 'All')
                          ? themeColors.background
                          : themeColors.text,

                      paddingHorizontal: 8,
                      paddingVertical: 4,
                    }}
                  >
                    {item?.title}
                  </Text>
                }
              />
            )}
            ItemSeparatorComponent={() => <View style={{ width: 10 }} />}
          />
        }
        contentContainerStyle={{
          backgroundColor: themeColors.background,
        }}
        renderItem={({ item }) => (
          <BlogCard
            data={item}
            onCardClick={() => {
              router.push({
                pathname: PATHS.link,
                params: {
                  url: item?.url,
                  userId: item?.createdBy,
                  fromPath: 'bookmarks',
                  title: item?.title,
                  tags: item?.tags,
                  id: item?.id,
                  mediumUrl: item?.isMedium,
                },
              })
            }}
            key={item?.id}
          />
        )}
        ItemSeparatorComponent={() => <View style={{ height: 10 }} />}
        ListEmptyComponent={() => (
          <NoDataUI
            text='No bookmarks found'
            containerStyle={{
              paddingTop: getPlatFormConstants()?.statusbarHeight + 24,
            }}
          />
        )}
        refreshControl={
          <RefreshControl refreshing={isLoading} onRefresh={() => refetch()} />
        }
      />
    </>
  )
}

export default Bookmarks
