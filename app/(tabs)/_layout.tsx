import { Tabs } from 'expo-router'
import React, { useState } from 'react'
import Ionicons from '@expo/vector-icons/Ionicons'
import { useTheme } from '@/theme'
import { COLORS, PATHS } from '@/constants'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import { useAppSelector } from '@/store'
import { rgba } from 'polished'

const TabLayout = () => {
  const { themeColors } = useTheme()
  const { hideTabBar } = useAppSelector(state => state.ui)
  const insets = useSafeAreaInsets()

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: COLORS.primary,
        headerShown: false,
        tabBarStyle: {
          height: hideTabBar ? 0 : 66 + insets.bottom,
          borderTopColor: rgba(themeColors.text, 0.2),
          paddingTop: 10,
          backgroundColor: themeColors.background,
        },

        tabBarLabelStyle: {
          fontSize: 14,
          paddingBottom: 8,
        },
      }}
      sceneContainerStyle={{
        backgroundColor: themeColors.background,
      }}
    >
      <Tabs.Screen
        name='index'
        options={{
          title: 'Home',
          tabBarIcon: ({ color }) => (
            <Ionicons name='home' size={22} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name={PATHS.bookmarks}
        options={{
          title: 'Bookmarks',
          tabBarIcon: ({ color }) => (
            <Ionicons name='bookmarks' size={22} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name={PATHS.profile}
        options={{
          title: 'Profile',
          unmountOnBlur: true,
          tabBarIcon: ({ color }) => (
            <Ionicons name='person' size={22} color={color} />
          ),
        }}
      />
    </Tabs>
  )
}

export default TabLayout
