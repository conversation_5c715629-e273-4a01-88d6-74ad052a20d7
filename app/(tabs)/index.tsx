import {
  Button,
  CustomModal,
  Input,
  NoDataUI,
  SiteIcon,
  SiteIconType,
  Tag,
} from '@/components'
import {
  COLORS,
  FONTS,
  getPlatFormConstants,
  PATHS,
  StorageKey,
} from '@/constants'
import { supabase } from '@/lib'
import { useAuth } from '@/lib/AuthProvider'
import { useCreateBookmarkMutation } from '@/store/api/bookmarks'
import { Flex, ScreenContainer } from '@/styles'
import { getBlogTitle, saveDataToLocalStorage } from '@/utils'
import Ionicons from '@expo/vector-icons/Ionicons'
import { router, Tabs, useFocusEffect } from 'expo-router'
import { useNavigation } from '@react-navigation/native'

import React, { useCallback, useEffect, useRef, useState } from 'react'
import {
  ActivityIndicator,
  Alert,
  BackHandler,
  Platform,
  Switch,
  Text,
  View,
} from 'react-native'
import { MultiSelect } from 'react-native-element-dropdown'
import { FloatingAction } from 'react-native-floating-action'
import WebView, { WebViewProps } from 'react-native-webview'
import { styles } from '../link'
import { rgba } from 'polished'
import { useTheme } from '@/theme'
import { useGetAllTagsQuery } from '@/store/api/tags'
import { setHideTabBar, useAppDispatch, useAppSelector } from '@/store'
import { useGetAllWebsitesQuery } from '@/store/api/websites'
import { useSafeAreaInsets } from 'react-native-safe-area-context'

type ActionType = 'link' | 'save' | 'back' | 'home'

const actions = [
  {
    text: 'Back',
    icon: <Ionicons name='arrow-back' size={20} color='white' />,
    name: 'back',
    position: 1,
  },
  {
    text: 'Home',
    icon: <Ionicons name='home' size={20} color='white' />,
    name: 'home',
    position: 2,
  },
  {
    text: 'Link',
    icon: <Ionicons name='link' size={20} color={'white'} />,
    name: 'link',
    position: 3,
  },
  {
    text: 'Save',
    icon: <Ionicons name='save' size={20} color={'white'} />,
    name: 'save',
    position: 4,
  },
]

const Home = () => {
  const { user } = useAuth()
  const [createBookmark, { isLoading }] = useCreateBookmarkMutation()
  const [showModal, setShowModal] = useState(false)
  const [isMedium, setIsMedium] = useState(false)
  const { themeColors } = useTheme()
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  const { data: tagsData } = useGetAllTagsQuery()
  const { data: websiteData, isLoading: websiteLoading } =
    useGetAllWebsitesQuery()
  const [selectedSite, setSelectedSite] = useState('')
  console.log('websiteLoading===[log]===>', websiteLoading)
  const navigation = useNavigation()

  const tagOptions = tagsData?.map((tag: { title: string }) => ({
    label: tag?.title,
    value: tag?.title,
  }))
  const toggleSwitch = () => setIsMedium(previousState => !previousState)

  const [currentPageUrl, setCurrentPageUrl] = useState('')
  const [bookmarkTitle, setBookmarkTitle] = useState('')
  const webviewRef = useRef<any>(null)
  const titleValue = getBlogTitle(currentPageUrl)
  const { hideTabBar } = useAppSelector(state => state.ui)
  const insets = useSafeAreaInsets()

  useEffect(() => {
    if (titleValue) {
      setBookmarkTitle(titleValue)
    }
  }, [titleValue])

  useFocusEffect(
    useCallback(() => {
      const onBackPress = () => {
        Alert.alert('Exit App', 'Are you sure you want to exit?', [
          {
            text: 'Cancel',
            onPress: () => null,
            style: 'cancel',
          },
          { text: 'YES', onPress: () => BackHandler.exitApp() },
        ])
        return true
      }

      if (Platform.OS === 'android') {
        BackHandler.addEventListener('hardwareBackPress', onBackPress)
      }

      return () => {
        if (Platform.OS === 'android') {
          BackHandler.removeEventListener('hardwareBackPress', onBackPress)
        }
      }
    }, [])
  )

  useEffect(() => {
    if (selectedSite) {
      navigation.setOptions({ tabBarStyle: { display: 'none' } })
    } else {
      navigation.setOptions({
        tabBarStyle: {
          display: 'flex',

          height: hideTabBar ? 0 : 66 + insets.bottom,
          borderTopColor: rgba('#fff', 0.2),
          paddingTop: 10,
          backgroundColor: themeColors.background,
        },
      })
    }
  }, [selectedSite, navigation])

  useEffect(() => {
    return () => {
      setSelectedSite('')
    }
  }, [])

  const handleBtnClick = (name: string) => {
    const selectedType = name as ActionType

    switch (selectedType) {
      case 'save':
        setShowModal(true)
        break
      case 'back':
        webviewRef?.current?.goBack()
        break
      case 'home':
        setSelectedSite('')
        break

      case 'link':
        router.push({
          pathname: PATHS.link,
          params: {
            url: currentPageUrl,
            userId: user?.id!,
          },
        })
        break
    }
  }

  const renderItem = (item: { value: string; label: string }) => {
    return (
      <View
        style={[
          styles.item,
          { backgroundColor: rgba(themeColors.inputBg, 0.8) },
        ]}
      >
        <Text
          style={[
            styles.selectedTextStyle,
            {
              color: themeColors.text,
            },
          ]}
        >
          {item.label}
        </Text>

        {selectedTags?.includes(item?.value) ? (
          <Ionicons name='checkmark' size={18} color={themeColors.text} />
        ) : null}
      </View>
    )
  }

  const handleAddClick = async () => {
    const { error } = await createBookmark({
      title: getBlogTitle(currentPageUrl),
      url: currentPageUrl,
      createdBy: user?.id!,
      tags: selectedTags!,
      isMedium,
    })

    if (!error) {
      setShowModal(false)
    }
  }

  return (
    <ScreenContainer>
      {true ? (
        <Flex cover justifyContent='center' alignItems='center'>
          <Flex direction='column' gap={10}>
            <ActivityIndicator
              animating={true}
              size='large'
              color={COLORS.primary}
            />
            <Text style={{ color: themeColors.text, ...FONTS.bodyRegular }}>
              Loading...
            </Text>
          </Flex>
        </Flex>
      ) : (
        <>
          {selectedSite ? (
            <>
              {getPlatFormConstants().isWeb ? (
                <iframe
                  src={selectedSite}
                  style={{ width: '100%', height: '100%', border: 'none' }}
                  title='Web Viewer'
                />
              ) : (
                <>
                  <WebView
                    onNavigationStateChange={event => {
                      setCurrentPageUrl(event.url)
                    }}
                    style={{ flex: 1 }}
                    ref={webviewRef}
                    startInLoadingState
                    source={{ uri: selectedSite }}
                  />
                </>
              )}

              <FloatingAction
                actions={actions}
                onPressItem={name => {
                  handleBtnClick(name!)
                }}
              />
              <CustomModal showModal={showModal} setShowModal={setShowModal}>
                <Flex
                  gap={16}
                  style={{
                    paddingHorizontal: 16,
                    paddingVertical: 30,
                    borderRadius: 8,
                    backgroundColor: themeColors.background,
                    width: '97%',
                  }}
                >
                  <Input
                    placeholder='Blog title'
                    label='Title'
                    value={bookmarkTitle}
                    defaultValue={getBlogTitle(currentPageUrl)}
                    onChange={setBookmarkTitle}
                  />

                  <Flex direction='row' gap={20}>
                    <Switch
                      trackColor={{ false: '#767577', true: COLORS.primary }}
                      thumbColor={'#f4f3f4'}
                      ios_backgroundColor='#3e3e3e'
                      onValueChange={toggleSwitch}
                      value={isMedium}
                    />
                    <Text style={{ color: themeColors.text }}>
                      Medium Website?
                    </Text>
                  </Flex>

                  <View style={{ width: '100%' }}>
                    <MultiSelect
                      style={[
                        styles.dropdown,
                        { backgroundColor: themeColors.inputBg },
                      ]}
                      placeholderStyle={[
                        styles.placeholderStyle,
                        { color: themeColors.text },
                      ]}
                      selectedTextStyle={styles.selectedTextStyle}
                      inputSearchStyle={styles.inputSearchStyle}
                      iconStyle={styles.iconStyle}
                      data={tagOptions}
                      labelField='label'
                      valueField='value'
                      placeholder='Select Tags'
                      value={selectedTags}
                      search
                      searchPlaceholder='Search Tags..'
                      onChange={setSelectedTags}
                      containerStyle={{
                        backgroundColor: themeColors.inputBg,
                        marginTop: -30,
                        borderWidth: 0,
                      }}
                      renderItem={renderItem}
                      renderSelectedItem={(item, unSelect) => (
                        <Tag
                          title={item?.label}
                          onPress={() => unSelect && unSelect(item)}
                          showIcon
                        />
                      )}
                    />
                    <Text
                      style={{
                        color: 'transparent',
                      }}
                    >
                      Lorem ipsum dolor sit, amet consectetur adipisicing elit.
                      Eaque
                    </Text>
                  </View>
                  <Flex
                    direction='row'
                    gap={8}
                    style={{ width: '100%', marginTop: 30 }}
                  >
                    <Button onPress={handleAddClick} isLoading={isLoading}>
                      Add
                    </Button>
                  </Flex>
                </Flex>
              </CustomModal>
            </>
          ) : (
            <>
              {!websiteData?.length && !websiteLoading ? (
                <>
                  <Flex
                    cover
                    style={{ backgroundColor: themeColors.background }}
                  >
                    <NoDataUI text='No Websites found' />
                  </Flex>
                </>
              ) : (
                <>
                  <Flex
                    cover
                    direction='row'
                    gap={20} //  TODO: make it dynamic
                    justifyContent='center'
                    alignItems='flex-start'
                    style={{
                      padding: 16,
                      flexWrap: 'wrap',
                    }}
                  >
                    {websiteData?.map((itm: SiteIconType) => (
                      <SiteIcon
                        icon_url={itm?.icon_url}
                        url={itm?.url}
                        key={itm?.url}
                        onIconClick={val => {
                          if (getPlatFormConstants().isWeb) {
                            window.navigator.clipboard.writeText(itm?.url)
                          }
                          setSelectedSite(val)
                        }}
                      />
                    ))}
                  </Flex>
                </>
              )}
            </>
          )}
        </>
      )}
    </ScreenContainer>
  )
}

export default Home
