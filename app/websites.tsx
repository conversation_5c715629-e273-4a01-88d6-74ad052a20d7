import { View, Text, ScrollView, Keyboard, Alert } from 'react-native'
import React, { useState } from 'react'
import { Stack } from 'expo-router'
import { Ionicons } from '@expo/vector-icons'
import { useTheme } from '@/theme'
import { Flex } from '@/styles'
import {
  Button,
  CustomModal,
  IconButton,
  Input,
  NoDataUI,
  Tag,
} from '@/components'
import { useGetAllTagsQuery } from '@/store/api/tags'
import {
  useCreateWebsiteMutation,
  useDeleteWebsiteMutation,
  useGetAllWebsitesQuery,
  useUpdateWebsiteMutation,
} from '@/store/api/websites'
import { useAuth } from '@/lib/AuthProvider'
import { showConfirmationDialog } from '@/utils'

const websites = () => {
  const { themeColors } = useTheme()
  const { data: websiteData, isLoading } = useGetAllWebsitesQuery()
  const [createWebsite, { isLoading: createWebsiteLoading }] =
    useCreateWebsiteMutation()
  const [updateWebsite, { isLoading: updateWebsiteLoading }] =
    useUpdateWebsiteMutation()
  const [deleteWebsite, { isLoading: deleteWebsiteLoading }] =
    useDeleteWebsiteMutation()

  const [selectedWebsite, setSelectedWebsite] = useState<any>({})
  const [showModal, setShowModal] = useState(false)
  const [url, setUrl] = useState('')
  const [name, setName] = useState('')
  const { user } = useAuth()

  const handleCreateWebsite = async () => {
    if (!name) {
      showConfirmationDialog('Please enter website name')

      return
    }
    Keyboard.dismiss()
    const { error } = await createWebsite({
      url: url,
      createdBy: user?.id!,
      name,
    })

    if (!error) {
      setUrl('')
      setName('')
      setShowModal(false)
    }
  }

  const handleUpdateWebsite = async () => {
    if (!name) {
      showConfirmationDialog('Please enter website name')
      return
    }
    Keyboard.dismiss()
    const { error } = await updateWebsite({
      name: name || selectedWebsite?.name!,
      url: url || selectedWebsite?.url!,
      id: selectedWebsite?.id!,
    })

    if (!error) {
      setSelectedWebsite({})
      setShowModal(false)
    }
  }

  const handleDeleteWebsite = async () => {
    const { error } = await deleteWebsite({
      tagId: selectedWebsite?.id,
    })

    if (!error) {
      setSelectedWebsite({})
      setShowModal(false)
    }
  }

  return (
    <>
      <Stack.Screen
        options={{
          title: 'Websites',
          headerRight: () => (
            <IconButton
              icon={
                <Ionicons name='add-sharp' size={24} color={themeColors.text} />
              }
              onPress={() => {
                setSelectedWebsite({})
                setShowModal(true)
              }}
            />
          ),
        }}
      />

      {!websiteData?.length ? (
        <Flex cover style={{ backgroundColor: themeColors.background }}>
          <NoDataUI text='No websites found' />
        </Flex>
      ) : (
        <ScrollView
          style={{
            flex: 1,
            backgroundColor: themeColors.background,
          }}
        >
          <Flex
            direction='row'
            style={{
              flex: 1,
              flexWrap: 'wrap',
              paddingVertical: 10,
              paddingHorizontal: 16,
            }}
            justifyContent='flex-start'
            gap={10}
          >
            {websiteData?.map((itm: any) => (
              <Tag
                title={itm?.name}
                key={itm?.id}
                onPress={() => {
                  setSelectedWebsite(itm)
                  setShowModal(true)
                }}
              />
            ))}
          </Flex>
        </ScrollView>
      )}

      <CustomModal showModal={showModal} setShowModal={setShowModal}>
        <Flex
          gap={32}
          style={{
            paddingHorizontal: 16,
            paddingVertical: 30,
            borderRadius: 8,
            // width: '92%',
            backgroundColor: themeColors.background,
          }}
        >
          <Flex gap={20}>
            <Input
              placeholder='Enter website name'
              label='Name'
              defaultValue={selectedWebsite?.name || name}
              onChange={setName}
            />
            <Input
              placeholder='Enter website url'
              label='Website'
              defaultValue={selectedWebsite?.url || url}
              onChange={setUrl}
            />
          </Flex>

          {Object?.values(selectedWebsite)?.length ? (
            <Flex direction='row' gap={20}>
              <Button
                style={{ flex: 1 }}
                isLoading={updateWebsiteLoading}
                onPress={handleUpdateWebsite}
              >
                Update
              </Button>
              <Button
                style={{ flex: 1 }}
                isLoading={deleteWebsiteLoading}
                onPress={handleDeleteWebsite}
                variant='destructive'
              >
                Delete
              </Button>
            </Flex>
          ) : (
            <Flex direction='row' gap={20}>
              <Button
                isLoading={createWebsiteLoading}
                onPress={handleCreateWebsite}
              >
                Save
              </Button>
            </Flex>
          )}
        </Flex>
      </CustomModal>
    </>
  )
}

export default websites
