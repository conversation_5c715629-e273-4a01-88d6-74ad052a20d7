import { getPlatFormConstants } from '@/constants'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { Alert } from 'react-native'

const isWeb = getPlatFormConstants().isWeb

export const saveDataToLocalStorage = async (key: string, value: any) => {
  try {
    if (isWeb) {
      localStorage.setItem(key, JSON.stringify(value))
    } else {
      await AsyncStorage.setItem(key, JSON.stringify(value))
    }
  } catch (error) {
    console.error('Failed to save data to localStorage', error)
  }
}

export const getDataFromLocalStorage = async (key: string) => {
  try {
    if (isWeb) {
      const value = localStorage.getItem(key) || ''
      return value ? JSON.parse(value) : undefined
    } else {
      const value = (await AsyncStorage.getItem(key)) || ''
      return value ? JSON.parse(value) : undefined
    }
  } catch (error) {
    console.error('Failed to get data from localStorage', error)
  }
}

export const clearLocalStorage = async () => {
  try {
    if (isWeb) {
      localStorage.clear()
    } else {
      await AsyncStorage.clear()
    }
  } catch (error) {
    console.error('Failed to clear localStorage', error)
  }
}

export const isSuccess = (response: any) => {
  const status = response?.status

  return status >= 200 && status < 300
}

export const getBlogTitle = (url: string) => {
  const parts = url?.split('/')?.filter(Boolean)
  const slug = parts[parts.length - 1]
  const titlePart = slug?.split('-')?.slice(0, -1)?.join(' ')
  const title = titlePart
    ?.split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ')

  return title
}

export const showConfirmationDialog = async (
  message: string,
  onConfirm?: () => void
) => {
  if (getPlatFormConstants().isWeb) {
    const isConfirmed = window.confirm(message)
    if (isConfirmed) {
      await onConfirm?.()
    }
  } else {
    Alert.alert(
      'Confirmation',
      message,
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Confirm', onPress: onConfirm },
      ],
      { cancelable: true }
    )
  }
}
