{"name": "readium", "version": "1.0.0", "main": "expo-router/entry", "scheme": "readium", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "lint": "eslint ."}, "dependencies": {"@expo/metro-runtime": "~3.2.3", "@expo/vector-icons": "^14.0.3", "@react-native-async-storage/async-storage": "1.23.1", "@reduxjs/toolkit": "^2.3.0", "@supabase/supabase-js": "^2.47.7", "aes-js": "^3.1.2", "base64-arraybuffer": "^1.0.2", "expo": "~51.0.28", "expo-constants": "~16.0.2", "expo-font": "~12.0.10", "expo-image-picker": "~15.0.7", "expo-linking": "~6.3.1", "expo-router": "~3.5.23", "expo-secure-store": "~13.0.2", "expo-splash-screen": "~0.27.6", "expo-status-bar": "~1.12.1", "polished": "^4.3.1", "react": "18.2.0", "react-dom": "18.2.0", "react-hook-form": "^7.54.1", "react-native": "0.74.5", "react-native-element-dropdown": "^2.12.2", "react-native-floating-action": "^1.22.0", "react-native-get-random-values": "~1.11.0", "react-native-google-places-autocomplete": "^2.5.7", "react-native-otp-entry": "^1.7.3", "react-native-safe-area-context": "4.10.5", "react-native-screens": "3.31.1", "react-native-toast-message": "^2.2.1", "react-native-web": "~0.19.10", "react-native-webview": "13.8.6", "react-redux": "^9.1.2", "redux-persist": "^6.0.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.45", "eslint": "^8.57.0", "eslint-config-expo": "~7.1.2", "eslint-plugin-import": "^2.31.0", "eslint-plugin-react-native": "^4.1.0", "typescript": "^5.1.3"}, "private": true}