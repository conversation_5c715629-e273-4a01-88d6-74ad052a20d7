import { AuthSession, Session, User } from '@supabase/supabase-js'
import {
  createContext,
  PropsWithChildren,
  useContext,
  useEffect,
  useState,
} from 'react'
import { supabase } from './supabase'
import { saveDataToLocalStorage } from '@/utils'
import { PATHS, StorageKey } from '@/constants'
import { setIsLoggedIn, useAppDispatch } from '@/store'
import { router } from 'expo-router'
import { ActivityIndicator } from 'react-native'
import React from 'react'

type AuthContextType = {
  session: AuthSession | null
  user: User | null
}

const AuthContext = createContext<AuthContextType>({
  session: null,
  user: null,
})

export default function AuthProvider({ children }: PropsWithChildren) {
  const [session, setSession] = useState<Session | null>(null)
  const [isReady, setIsReady] = useState(false)
  const dispatch = useAppDispatch()

  useEffect(() => {
    try {
      supabase.auth.onAuthStateChange((event, session) => {
        if ((event === 'SIGNED_IN' || event === 'INITIAL_SESSION') && session) {
          setSession(session)
          dispatch(setIsLoggedIn(true))
          router.replace(PATHS.tabs)
        }

        if (event === 'PASSWORD_RECOVERY') {
          router.replace(PATHS.resetPassword)
        }

        if (event === 'SIGNED_OUT') {
          setSession(null)
          dispatch(setIsLoggedIn(false))
          saveDataToLocalStorage(StorageKey.isLoggedIn, false)
        }
      })
    } catch (error) {
    } finally {
      setIsReady(true)
    }
  }, [])

  if (!isReady) {
    return <ActivityIndicator />
  }

  return (
    <AuthContext.Provider value={{ session, user: session?.user || null }}>
      {children}
    </AuthContext.Provider>
  )
}

export const useAuth = () => useContext(AuthContext)
