import { StorageKey } from '@/constants'
import { saveDataToLocalStorage } from '@/utils'
import { createSlice } from '@reduxjs/toolkit'

interface UIState {
  isOnBoardingDone: boolean
  isLoggedIn: boolean
  hideTabBar: boolean
}

const initialState: UIState = {
  isOnBoardingDone: false,
  isLoggedIn: false,
  hideTabBar: false,
}

const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    setOnBoardingState: (state, action: { payload: boolean }) => {
      state.isOnBoardingDone = action.payload
    },
    setIsLoggedIn: (state, action: { payload: boolean }) => {
      state.isLoggedIn = action.payload

      saveDataToLocalStorage(StorageKey.isLoggedIn, action.payload)
    },
    setHideTabBar: (state, action: { payload: boolean }) => {
      state.hideTabBar = action.payload
    },
  },
})

export const { setOnBoardingState, setIsLoggedIn, setHideTabBar } =
  uiSlice.actions

export default uiSlice.reducer
