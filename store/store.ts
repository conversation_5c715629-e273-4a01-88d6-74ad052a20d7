import { combineReducers, configureStore } from '@reduxjs/toolkit'
import uiReducer from './slices/uiSlice'
import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux'
import { authApi } from './api/auth'
import { bookmarksApi } from './api/bookmarks'
import { tagsApi } from './api/tags'
import { profileApi } from './api/profile'
import { websitesApi } from './api/websites'

const rootReducer = combineReducers({
  ui: uiReducer,
  [authApi.reducerPath]: authApi.reducer,
  [bookmarksApi.reducerPath]: bookmarksApi.reducer,
  [tagsApi.reducerPath]: tagsApi.reducer,
  [profileApi.reducerPath]: profileApi.reducer,
  [websitesApi.reducerPath]: websitesApi.reducer,
})

export const store = configureStore({
  reducer: rootReducer,
  middleware: getDefaultMiddleware =>
    getDefaultMiddleware({}).concat(
      authApi.middleware,
      bookmarksApi.middleware,
      tagsApi.middleware,
      profileApi.middleware,
      websitesApi.middleware
    ),
})

export type RootState = ReturnType<typeof store.getState>
export type AppDispatch = typeof store.dispatch

export const useAppDispatch = () => useDispatch<AppDispatch>()
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector
