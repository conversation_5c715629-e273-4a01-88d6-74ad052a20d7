import { supabase } from '@/lib'
import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'
import { decode } from 'base64-arraybuffer'
import Toast from 'react-native-toast-message'

export const profileApi = createApi({
  reducerPath: 'profileApi',
  baseQuery: fetchBaseQuery({ baseUrl: '/' }),
  tagTypes: ['profile'],
  endpoints: builder => ({
    getProfile: builder.query<any, void>({
      queryFn: async () => {
        const {
          data: { user },
        } = await supabase.auth.getUser()

        return { data: user }
      },
      providesTags: ['profile'],
    }),
    updateProfile: builder.mutation<
      any,
      { fileTitle?: string; file?: any; password?: string }
    >({
      queryFn: async ({ fileTitle, file, ...rest }) => {
        let url: string | null = null
        let res: any
        if (fileTitle) {
          const { data, error } = await supabase.storage
            .from('avatars')
            .upload(fileTitle!, decode(file), {
              cacheControl: '3600',
              upsert: false,
              contentType: 'image/*',
            })

          if (!error) {
            const {
              data: { publicUrl },
            } = supabase.storage.from('avatars').getPublicUrl(fileTitle!)
            url = publicUrl
          }

          const { error: metadataError } = await supabase.auth.updateUser({
            data: {
              avatar_url: url,
            },
          })

          if (!metadataError) {
            Toast.show({
              type: 'success',
              text1: 'Success',
              text2: 'Avatar added successfully',
            })
          } else {
            Toast.show({
              type: 'error',
              text1: 'Error',
              text2: error?.message,
            })
          }
          res = data
        } else {
          const { data, error: metadataError } = await supabase.auth.updateUser(
            {
              data: {
                ...rest,
              },
            }
          )

          if (!metadataError) {
            Toast.show({
              type: 'success',
              text1: 'Success',
              text2: 'Profile updated successfully',
            })
          } else {
            Toast.show({
              type: 'error',
              text1: 'Error',
              text2: metadataError?.message,
            })
          }
          res = null
        }

        return { data: res }
      },
      invalidatesTags: ['profile'],
    }),
  }),
})

export const { useUpdateProfileMutation, useGetProfileQuery } = profileApi
