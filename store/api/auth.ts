import { supabase } from '@/lib'
import {
  createApi,
  fetchBase<PERSON>uery,
  FetchBaseQueryError,
} from '@reduxjs/toolkit/query/react'
import { AuthError, AuthResponse, EmailOtpType } from '@supabase/supabase-js'
import Toast from 'react-native-toast-message'

const handleAuthError = (error: any) => {
  return error instanceof AuthError ? error.message : 'An error occurred'
}

type AuthCredentials = {
  email: string
  password: string
}

type OtpVerification = {
  email: string
  otp: string
}

const executeAuthRequest = async <T>(
  action: () => Promise<{ data: T; error: any }>
) => {
  try {
    const response = await action()

    if (response?.error?.message) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: response.error.message,
      })
    }

    return { data: response.data }
  } catch (error) {
    return {
      error: {
        status: 'CUSTOM_ERROR',
        data: handleAuthError(error),
        error: handleAuthError(error),
      } as FetchBaseQueryError,
    }
  }
}

export const authApi = createApi({
  reducerPath: 'authApi',
  baseQuery: fetchBaseQuery({ baseUrl: '/' }),
  endpoints: builder => ({
    createUser: builder.mutation<AuthResponse['data'], AuthCredentials>({
      queryFn: ({ email, password }) =>
        executeAuthRequest(() =>
          supabase.auth.signUp({
            email,
            password,
          })
        ),
    }),

    loginUser: builder.mutation<AuthResponse['data'], AuthCredentials>({
      queryFn: ({ email, password }) => {
        return executeAuthRequest<AuthResponse['data']>(() =>
          supabase.auth.signInWithPassword({
            email,
            password,
          })
        )
      },
    }),

    forgotPassword: builder.mutation<
      AuthResponse['data'],
      Pick<AuthCredentials, 'email'>
    >({
      queryFn: ({ email }) =>
        executeAuthRequest<any>(() =>
          supabase.auth.resetPasswordForEmail(email)
        ),
    }),

    verifyOtp: builder.mutation<
      AuthResponse['data'],
      OtpVerification & { type: EmailOtpType }
    >({
      queryFn: ({ otp, email, type }) =>
        executeAuthRequest(() =>
          supabase.auth.verifyOtp({
            email,
            token: otp,
            type: type!,
          })
        ),
    }),
    signOut: builder.mutation<any, void>({
      queryFn: () => executeAuthRequest<any>(() => supabase.auth.signOut()),
    }),
  }),
})

export const {
  useCreateUserMutation,
  useLoginUserMutation,
  useForgotPasswordMutation,
  useVerifyOtpMutation,
  useSignOutMutation,
} = authApi
