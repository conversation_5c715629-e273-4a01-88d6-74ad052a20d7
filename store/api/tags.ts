import { supabase } from '@/lib'
import { isSuccess } from '@/utils'
import {
  createApi,
  fetchBaseQuery,
  FetchBaseQueryError,
} from '@reduxjs/toolkit/query/react'
import Toast from 'react-native-toast-message'

export const tagsApi = createApi({
  reducerPath: 'tagsApi',
  baseQuery: fetchBaseQuery({ baseUrl: '/' }),
  tagTypes: ['tags'],
  endpoints: builder => ({
    createTag: builder.mutation<any, { title: string; createdBy: string }>({
      queryFn: async ({ title, createdBy }) => {
        const response = await supabase.from('tags').insert({
          title,
          created_by: createdBy,
        })

        if (isSuccess(response)) {
          Toast.show({
            type: 'success',
            text1: 'Success',
            text2: 'Tag created successfully',
          })
        } else {
          Toast.show({
            type: 'error',
            text1: 'Error',
            text2: 'Error while creating Tag',
          })
        }

        return { data: response.data }
      },
      invalidatesTags: ['tags'],
    }),
    deleteTag: builder.mutation<any, { tagId: string }>({
      queryFn: async ({ tagId }) => {
        const response = await supabase.from('tags').delete().eq('id', tagId)
        if (isSuccess(response)) {
          Toast.show({
            type: 'success',
            text1: 'Success',
            text2: 'Tag Deleted successfully',
          })
        } else {
          Toast.show({
            type: 'error',
            text1: 'Error',
            text2: 'Error while Deleting Tag',
          })
        }

        return { data: response.data }
      },
      invalidatesTags: ['tags'],
    }),
    updateTag: builder.mutation<any, { tagId: string; title: string }>({
      queryFn: async ({ tagId, title }) => {
        const response = await supabase
          .from('tags')
          .update({
            title,
          })
          .eq('id', tagId)
        if (isSuccess(response)) {
          Toast.show({
            type: 'success',
            text1: 'Success',
            text2: 'Tag Updated successfully',
          })
        } else {
          Toast.show({
            type: 'error',
            text1: 'Error',
            text2: 'Error while Updating Tag',
          })
        }

        return { data: response.data }
      },
      invalidatesTags: ['tags'],
    }),
    getAllTags: builder.query<any, void>({
      queryFn: async () => {
        const response = await supabase.from('tags').select('*').order('id', {
          ascending: false,
        })

        return { data: response.data }
      },
      providesTags: ['tags'],
    }),
  }),
})

export const {
  useCreateTagMutation,
  useGetAllTagsQuery,
  useDeleteTagMutation,
  useUpdateTagMutation,
} = tagsApi
