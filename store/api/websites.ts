import { supabase } from '@/lib'
import { isSuccess } from '@/utils'
import {
  createApi,
  fetchBaseQuery,
  FetchBaseQueryError,
} from '@reduxjs/toolkit/query/react'
import Toast from 'react-native-toast-message'

const getFaviconUrl = (url: string) => {
  return `https://s2.googleusercontent.com/s2/favicons?domain_url=${url}`
}

export const websitesApi = createApi({
  reducerPath: 'websitesApi',
  baseQuery: fetchBaseQuery({ baseUrl: '/' }),
  tagTypes: ['websites'],
  endpoints: builder => ({
    createWebsite: builder.mutation<
      any,
      { createdBy: string; url: string; name: string }
    >({
      queryFn: async ({ createdBy, url, name }) => {
        const response = await supabase.from('websites').insert({
          icon_url: getFaviconUrl(url),
          url,
          created_by: createdBy,
          name,
        })

        if (isSuccess(response)) {
          Toast.show({
            type: 'success',
            text1: 'Success',
            text2: 'Website created successfully',
          })
        } else {
          Toast.show({
            type: 'error',
            text1: 'Error',
            text2: 'Error while creating Website',
          })
        }

        return { data: response.data }
      },
      invalidatesTags: ['websites'],
    }),
    deleteWebsite: builder.mutation<any, { tagId: string }>({
      queryFn: async ({ tagId }) => {
        const response = await supabase
          .from('websites')
          .delete()
          .eq('id', tagId)
        if (isSuccess(response)) {
          Toast.show({
            type: 'success',
            text1: 'Success',
            text2: 'Website Deleted successfully',
          })
        } else {
          Toast.show({
            type: 'error',
            text1: 'Error',
            text2: 'Error while Deleting Website',
          })
        }

        return { data: response.data }
      },
      invalidatesTags: ['websites'],
    }),
    updateWebsite: builder.mutation<
      any,
      { id: string; name?: string; url?: string }
    >({
      queryFn: async ({ name, id, url }) => {
        const response = await supabase
          .from('websites')
          .update({
            name,
            url,
          })
          .eq('id', id)
        if (isSuccess(response)) {
          Toast.show({
            type: 'success',
            text1: 'Success',
            text2: 'Website Updated successfully',
          })
        } else {
          Toast.show({
            type: 'error',
            text1: 'Error',
            text2: 'Error while Updating Website',
          })
        }

        return { data: response.data }
      },
      invalidatesTags: ['websites'],
    }),
    getAllWebsites: builder.query<any, void>({
      queryFn: async () => {
        const response = await supabase
          .from('websites')
          .select('*')
          .order('id', {
            ascending: false,
          })

        return { data: response.data }
      },
      providesTags: ['websites'],
    }),
  }),
})

export const {
  useCreateWebsiteMutation,
  useGetAllWebsitesQuery,
  useDeleteWebsiteMutation,
  useUpdateWebsiteMutation,
} = websitesApi
