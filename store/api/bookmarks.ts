import { supabase } from '@/lib'
import { isSuccess } from '@/utils'
import {
  createApi,
  fetchBaseQuery,
  FetchBaseQueryError,
} from '@reduxjs/toolkit/query/react'
import Toast from 'react-native-toast-message'

export const bookmarksApi = createApi({
  reducerPath: 'bookmarksApi',
  baseQuery: fetchBaseQuery({ baseUrl: '/' }),
  tagTypes: ['bookmarks'],
  endpoints: builder => ({
    createBookmark: builder.mutation<
      any,
      {
        title: string
        url: string
        createdBy: string
        tags: string[]
        isMedium?: boolean
      }
    >({
      queryFn: async ({ title, url, createdBy, tags, isMedium }) => {
        const response = await supabase.from('bookmarks').insert({
          title,
          url,
          created_by: createdBy,
          tags,
          isMedium,
        })

        if (isSuccess(response)) {
          Toast.show({
            type: 'success',
            text1: 'Success',
            text2: 'Bookmark created successfully',
          })
        } else {
          Toast.show({
            type: 'error',
            text1: 'Error',
            text2: 'Error while creating bookmark',
          })
        }

        return { data: response.data }
      },
      invalidatesTags: ['bookmarks'],
    }),
    deleteBookmark: builder.mutation<any, { id: string }>({
      queryFn: async ({ id }) => {
        const response = await supabase.from('bookmarks').delete().eq('id', id)

        if (isSuccess(response)) {
          Toast.show({
            type: 'success',
            text1: 'Success',
            text2: 'Bookmark deleted successfully',
          })
        } else {
          Toast.show({
            type: 'error',
            text1: 'Error',
            text2: 'Error while deleting bookmark',
          })
        }

        return { data: response.data }
      },
      invalidatesTags: ['bookmarks'],
    }),
    updateBookmark: builder.mutation<any, { id: string; data: any }>({
      queryFn: async ({ id, data }) => {
        const response = await supabase
          .from('bookmarks')
          .update({
            title: data?.title,
            tags: data?.tags,
            isMedium: data?.isMedium,
          })
          .eq('id', id)

        if (isSuccess(response)) {
          Toast.show({
            type: 'success',
            text1: 'Success',
            text2: 'Bookmark updated successfully',
          })
        } else {
          Toast.show({
            type: 'error',
            text1: 'Error',
            text2: 'Error while updating bookmark',
          })
        }

        return { data: response.data }
      },
      invalidatesTags: ['bookmarks'],
    }),
    getAllBookmarks: builder.query<any, void>({
      queryFn: async () => {
        const response = await supabase
          .from('bookmarks')
          .select('*')
          .order('id', {
            ascending: false,
          })

        return { data: response.data }
      },
      providesTags: ['bookmarks'],
    }),
  }),
})

export const {
  useCreateBookmarkMutation,
  useGetAllBookmarksQuery,
  useDeleteBookmarkMutation,
  useUpdateBookmarkMutation,
} = bookmarksApi
