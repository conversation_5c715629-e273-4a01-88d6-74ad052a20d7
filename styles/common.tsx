import {
  ScrollView,
  StatusBar,
  StyleSheet,
  View,
  ViewProps,
  ViewStyle,
} from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { useTheme } from '../theme'

interface FlexProps extends ViewProps {
  gap?: number
  cover?: boolean
  direction?: 'row' | 'column'
  alignItems?: 'flex-start' | 'flex-end' | 'center' | 'stretch' | 'baseline'
  justifyContent?:
    | 'flex-start'
    | 'flex-end'
    | 'center'
    | 'space-between'
    | 'space-around'
    | 'space-evenly'
  fullWidth?: boolean
  children: React.ReactNode
}

export const Flex = (props: FlexProps) => {
  const {
    children,
    style,
    cover,
    gap,
    fullWidth = true,
    direction = 'column',
    ...rest
  } = props
  return (
    <View
      style={[
        styles.container,
        {
          flexDirection: direction,
          gap: gap || 0,
          width: fullWidth ? '100%' : 'auto',
          flex: cover ? 1 : undefined,
        },
        style,
      ]}
      {...rest}
    >
      {children}
    </View>
  )
}

export const ScreenContainer = ({
  children,
  style,
}: {
  children: React.ReactNode
  style?: ViewStyle
}) => {
  const { themeColors, dark } = useTheme()

  return (
    <SafeAreaView
      style={{
        flex: 1,
        backgroundColor: themeColors.background,
        ...style,
      }}
    >
      <StatusBar barStyle={dark ? 'light-content' : 'dark-content'} />
      {children}
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
  },
})
