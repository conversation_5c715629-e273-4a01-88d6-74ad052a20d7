import { View, Text, Image, TouchableOpacity } from 'react-native'
import React from 'react'
import { Flex } from '@/styles'
import { Ionicons } from '@expo/vector-icons'
import { FONTS } from '@/constants'
import { Icon } from '@expo/vector-icons/build/createIconSet'
import { useTheme } from '@/theme'

export const MenuItem = ({
  iconName,
  title,
  onPress,
  renderRightContent,
  isLogOut,
}: {
  iconName: any
  title: string
  onPress: () => void
  renderRightContent?: () => React.ReactNode
  isLogOut?: boolean
}) => {
  const { themeColors } = useTheme()
  return (
    <TouchableOpacity
      onPress={onPress}
      style={{
        flex: 1,
        width: '100%',
        paddingHorizontal: 20,
      }}
    >
      <Flex direction='row' justifyContent='space-between'>
        <Flex
          direction='row'
          style={{
            justifyContent: 'flex-start',
            paddingVertical: 10,
            width: '100%',
          }}
          fullWidth={false}
          gap={20}
          alignItems='center'
        >
          <Ionicons
            name={iconName}
            size={24}
            color={isLogOut ? 'red' : themeColors.text}
          />
          <Text
            style={{
              ...FONTS.bodyLg,
              fontSize: 20,
              color: isLogOut ? 'red' : themeColors.text,
            }}
          >
            {title}
          </Text>
        </Flex>
        {renderRightContent ? (
          renderRightContent?.()
        ) : (
          <Ionicons
            name='chevron-forward-sharp'
            size={24}
            color={themeColors.text}
          />
        )}
      </Flex>
    </TouchableOpacity>
  )
}
