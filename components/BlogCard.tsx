import { Flex } from '@/styles'
import { useTheme } from '@/theme'
import { rgba } from 'polished'
import React from 'react'
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Platform,
} from 'react-native'
import { Tag } from './Tag'

interface BlogCardProps {
  title: string
  url: string
  tags: string[]
}

export const BlogCard = ({
  data: { url, title, tags },
  onCardClick,
}: {
  data: BlogCardProps
  onCardClick: () => void
}) => {
  const { themeColors } = useTheme()
  return (
    <TouchableOpacity
      style={[
        styles.card,
        {
          backgroundColor: themeColors.background,
          shadowColor: themeColors.text,
        },
      ]}
      onPress={onCardClick}
    >
      <View style={styles.content}>
        <Text
          style={[
            styles.title,
            {
              color: themeColors.text,
            },
          ]}
        >
          {title}
        </Text>
        <Text
          style={[
            styles.excerpt,
            {
              color: rgba(themeColors.text, 0.5),
            },
          ]}
          numberOfLines={3}
        >
          {url}
        </Text>

        <Flex
          direction='row'
          style={{
            flexWrap: 'wrap',
            justifyContent: 'flex-start',
          }}
          gap={12}
        >
          {tags?.map(tag => (
            <Tag title={tag} isSmall key={tag} />
          ))}
        </Flex>
      </View>
    </TouchableOpacity>
  )
}

const styles = StyleSheet.create({
  card: {
    borderRadius: 8,
    marginBottom: 16,
    marginHorizontal: 8,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        borderColor: rgba("#fff", 0.5),
      },
      android: {
        elevation: 3,
      },
      web: {
        boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)',
        border: `1px solid ${rgba('#fff', 0.2)}`,
        borderColor: rgba('#fff', 0.2),
      },
    }),
  },
  content: {
    padding: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#333',
  },
  excerpt: {
    fontSize: 14,
    color: '#666',
    marginBottom: 16,
  },
})
