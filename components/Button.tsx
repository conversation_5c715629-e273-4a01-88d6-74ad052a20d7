import {
  ActivityIndicator,
  Pressable,
  PressableProps,
  StyleSheet,
  Text,
  TextStyle,
  View,
  ViewStyle,
} from 'react-native'
import { Flex } from '../styles'
import { useTheme } from '../theme'
import { COLORS, FONTS, SIZES } from '../constants'
import { rgba } from 'polished'

interface ButtonProps extends PressableProps {
  children: React.ReactNode
  variant?: 'outline' | 'destructive'
  isLoading?: boolean
  style?: ViewStyle
  btnTextStyle?: TextStyle
}

export const Button = (props: ButtonProps) => {
  const { children, variant, isLoading, style, btnTextStyle, ...rest } = props
  const isDisabled = isLoading || rest.disabled || false
  const isOutline = variant === 'outline'
  const isDanger = variant === 'destructive'

  return (
    <Pressable
      {...rest}
      style={({ pressed }) => [
        styles.btnContainer,

        {
          backgroundColor: pressed ? rgba(COLORS.primary, 0.5) : COLORS.primary,
        },
        isDisabled && styles.btnDisabled,
        isOutline && styles.btnOutlineContainer,
        isDanger && {
          backgroundColor: COLORS.error,
        },
        { ...style },
      ]}
      disabled={isDisabled}
    >
      {isLoading ? (
        <ActivityIndicator
          animating={true}
          size='small'
          color={isOutline ? COLORS.primary : COLORS.white}
        />
      ) : (
        <Text
          style={[
            styles.btnText,
            {
              color: isOutline ? COLORS.primary : COLORS.white,
            },
          ]}
        >
          {children}
        </Text>
      )}
    </Pressable>
  )
}

const styles = StyleSheet.create({
  btnContainer: {
    width: '100%',
    backgroundColor: COLORS.primary,
    padding: SIZES.paddingMd,
    borderRadius: SIZES.radiusLg,
  },
  btnDisabled: {
    backgroundColor: rgba(COLORS.primary, 0.7),
    // opacity: 0.5,
  },
  btnOutlineContainer: {
    backgroundColor: 'transparent',
    borderColor: COLORS.primary,
    borderWidth: 1,
  },
  btnText: {
    textAlign: 'center',
    ...FONTS.medium,
    color: COLORS.white,
  },
})
