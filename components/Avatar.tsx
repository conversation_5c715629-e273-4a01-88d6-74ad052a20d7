import {
  Image,
  StyleSheet,
  ImageSourcePropType,
  TouchableOpacity,
} from 'react-native'
import React, { useState } from 'react'
import { Ionicons } from '@expo/vector-icons'
import { Button } from './Button'
import * as ImagePicker from 'expo-image-picker'
import { useUpdateProfileMutation } from '@/store/api/profile'

export const Avatar = ({ source }: { source?: string }) => {
  const [updateProfile, { isLoading }] = useUpdateProfileMutation()
  const [selectedImage, setSelectedImage] = useState<string | undefined>(
    undefined
  )

  const pickImageAsync = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      quality: 1,
      base64: true,
    })

    if (!result.canceled) {
      setSelectedImage(result.assets?.[0]?.uri)

      await updateProfile({
        fileTitle: `${Date.now()}`,
        file: result.assets?.[0]?.base64,
      })
    } else {
      // alert('You did not select any image.')
    }
  }
  return (
    <TouchableOpacity style={styles.avatarContainer} onPress={pickImageAsync}>
      <>
        <Image source={{ uri: source! }} style={styles.avatar} />
        <Button style={styles.buttonContainer} onPress={pickImageAsync}>
          <Ionicons name='pencil' size={20} color={'white'} />
        </Button>
      </>
    </TouchableOpacity>
  )
}

const styles = StyleSheet.create({
  avatar: {
    width: 150,
    height: 150,
    borderRadius: 75,
    // backgroundColor: 'yellow',
  },
  avatarContainer: {
    width: 'auto',
    position: 'relative',
    alignSelf: 'flex-start',
    marginHorizontal: 'auto',
  },
  buttonContainer: {
    width: 'auto',
    position: 'absolute',
    bottom: 20,
    right: -4,
    borderRadius: 8,
    paddingHorizontal: 10,
    paddingVertical: 6,
  },
})
