import { useTheme } from '@/theme'
import React, { useState, ReactNode } from 'react'
import {
  TouchableOpacity,
  View,
  Text,
  StyleSheet,
  Animated,
  StyleProp,
  ViewStyle,
  TextStyle,
} from 'react-native'

interface IconButtonProps {
  icon: ReactNode
  onPress?: () => void
  tooltipText?: string
  style?: StyleProp<ViewStyle>
  iconStyle?: StyleProp<ViewStyle>
  tooltipStyle?: StyleProp<ViewStyle>
  holdStyle?: StyleProp<ViewStyle>
  activeOpacity?: number
  tooltipDelay?: number
  disabled?: boolean
}

export const IconButton: React.FC<IconButtonProps> = ({
  icon,
  onPress,
  tooltipText,
  style,
  iconStyle,
  tooltipStyle,
  holdStyle,
  activeOpacity = 0.7,
  tooltipDelay = 500,
  disabled = false,
}) => {
  const [isHolding, setIsHolding] = useState<boolean>(false)
  const [showTooltip, setShowTooltip] = useState<boolean>(false)
  const scaleValue = new Animated.Value(1)
  const { themeColors } = useTheme()
  const tooltipTimerRef = React.useRef<NodeJS.Timeout | null>(null)

  const handlePressIn = () => {
    if (disabled) return

    setIsHolding(true)
    setShowTooltip(false)

    // Start tooltip timer
    tooltipTimerRef.current = setTimeout(() => {
      setShowTooltip(true)
    }, tooltipDelay)

    // Scale animation for hold effect
    Animated.spring(scaleValue, {
      toValue: 0.9,
      friction: 3,
      tension: 40,
      useNativeDriver: true,
    }).start()
  }

  const handlePressOut = () => {
    if (disabled) return

    // Clear tooltip timer if not triggered
    if (tooltipTimerRef.current) {
      clearTimeout(tooltipTimerRef.current)
    }

    setIsHolding(false)
    setShowTooltip(false)

    // Return to original scale
    Animated.spring(scaleValue, {
      toValue: 1,
      friction: 3,
      tension: 40,
      useNativeDriver: true,
    }).start()
  }

  return (
    <View style={styles.container}>
      <Animated.View
        style={[
          styles.buttonContainer,
          style,
          isHolding && { backgroundColor: themeColors.inputBg },
          { transform: [{ scale: scaleValue }] },
          disabled && styles.disabledButton,
        ]}
      >
        <TouchableOpacity
          activeOpacity={activeOpacity}
          onPress={onPress}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          disabled={disabled}
        >
          <View style={iconStyle}>{icon}</View>
        </TouchableOpacity>
      </Animated.View>

      {showTooltip && tooltipText && (
        <View style={[styles.tooltipContainer, tooltipStyle]}>
          <Text style={styles.tooltipText}>{tooltipText}</Text>
        </View>
      )}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonContainer: {
    borderRadius: 50,
    padding: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  disabledButton: {
    opacity: 0.5,
  },
  tooltipContainer: {
    position: 'absolute',
    top: -40,
    backgroundColor: 'rgba(0,0,0,0.7)',
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 5,
  },
  tooltipText: {
    color: 'white',
    fontSize: 12,
  },
})
