import { StyleProp, Text, ViewStyle } from 'react-native'
import { FONTS } from '../constants'
import { Flex } from '../styles'
import { useTheme } from '../theme'
import React from 'react'

export const NoDataUI = ({
  text,
  containerStyle,
}: {
  text: string
  containerStyle?: ViewStyle
}) => {
  const { themeColors } = useTheme()
  return (
    <Flex
      cover
      style={{
        backgroundColor: themeColors.background,
        ...containerStyle,
      }}
    >
      <Text style={{ ...FONTS.h2, color: themeColors.text }}>{text}</Text>
    </Flex>
  )
}
