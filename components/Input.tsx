import { forwardRef, LegacyRef, useState } from 'react'
import Ionicons from '@expo/vector-icons/Ionicons'
import { rgba } from 'polished'
import {
  View,
  TextInput,
  TextInputProps,
  StyleSheet,
  TouchableOpacity,
  ViewStyle,
  TextStyle,
  Text,
  Platform,
} from 'react-native'

import { useTheme } from '@/theme'
import { COLORS } from '@/constants'
import { Flex } from '@/styles'

interface CustomTextInputProps extends TextInputProps {
  icon?: React.ReactNode
  iconPosition?: 'left' | 'right'
  onIconPress?: () => void
  isPassword?: boolean
  containerStyle?: ViewStyle
  inputStyle?: TextStyle
  error?: string
  onChange: any
  label?: string
}

export const Input = forwardRef(
  (props: CustomTextInputProps, ref: LegacyRef<TextInput>) => {
    const {
      icon,
      iconPosition = 'left',
      onIconPress,
      containerStyle,
      isPassword,
      inputStyle,
      error,
      label,
      onChange,
      ...rest
    } = props

    const { themeColors, dark } = useTheme()
    const [showPassword, setShowPassword] = useState(false)
    const [isFocused, setIsFocused] = useState(false)

    const secureTextEntry = isPassword && !showPassword

    const getBgColor = () => {
      if (error) return rgba(COLORS.error, 0.1)
      if (isFocused) return dark ? themeColors.background : COLORS.lightBlue
      return themeColors.inputBg
    }

    const getBorderColor = () => {
      if (error) return COLORS.error
      if (isFocused) return themeColors.primary
      return dark ? rgba(COLORS.borderColor, 0.3) : COLORS.borderColor
    }

    const handlePasswordToggle = () => setShowPassword(prev => !prev)

    const renderIcon = () => {
      if (isPassword) {
        return (
          <Ionicons
            name={showPassword ? 'eye-off' : 'eye'}
            size={24}
            color={themeColors.text}
          />
        )
      }
      return icon
    }

    return (
      <Flex gap={8} alignItems='flex-start' style={{ width: '100%' }}>
        <Flex gap={6} alignItems='flex-start'>
          {!!label ? (
            <Text style={[styles.labelText, { color: themeColors.text }]}>
              {label}
            </Text>
          ) : null}

          <View
            style={[
              styles.container,
              {
                borderColor: getBorderColor(),
                backgroundColor: getBgColor(),
              },
              containerStyle,
            ]}
          >
            {/* Left Icon */}
            {(icon || isPassword) && iconPosition === 'left' && (
              <TouchableOpacity
                onPress={isPassword ? handlePasswordToggle : onIconPress}
                style={styles.iconContainer}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                {renderIcon()}
              </TouchableOpacity>
            )}

            <TextInput
              ref={ref}
              secureTextEntry={secureTextEntry}
              placeholderTextColor={rgba(themeColors.text, 0.5)}
              onFocus={() => setIsFocused(true)}
              autoCapitalize='none'
              style={[
                styles.input,
                {
                  color: themeColors.text,
                  paddingLeft:
                    (icon || isPassword) && iconPosition === 'left' ? 10 : 16,
                  paddingRight:
                    (icon || isPassword) && iconPosition === 'right' ? 10 : 16,
                },
                inputStyle,
              ]}
              onChangeText={onChange}
              {...rest}
              onBlur={() => setIsFocused(false)}
            />

            {/* Right Icon */}
            {(icon || isPassword) && iconPosition === 'right' && (
              <TouchableOpacity
                onPress={isPassword ? handlePasswordToggle : onIconPress}
                style={styles.iconContainer}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                {renderIcon()}
              </TouchableOpacity>
            )}
          </View>
        </Flex>

        {/* Error Message */}
        {error && (
          <Text style={[styles.errorText, { color: COLORS.error }]}>
            {error}
          </Text>
        )}
      </Flex>
    )
  }
)

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    borderRadius: 8,
    borderWidth: 1,
    flexDirection: 'row',
    minHeight: 48,
    width: '100%',
  },

  input: {
    flex: 1,
    fontSize: 16,
    paddingVertical: 12,
    ...Platform.select({
      web: {
        outlineWidth: 0,
      },
    }),
  },
  iconContainer: {
    paddingHorizontal: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  labelText: {
    fontSize: 16,
    lineHeight: 16,
    alignSelf: 'flex-start',
  },
  errorText: {
    fontSize: 12,
    lineHeight: 16,
    alignSelf: 'flex-start',
  },
})
