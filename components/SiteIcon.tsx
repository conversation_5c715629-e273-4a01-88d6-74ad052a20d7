import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native'
import React from 'react'
import { Ionicons } from '@expo/vector-icons'

export interface SiteIconType {
  icon_url: string
  url: string
}

export const SiteIcon = ({
  icon_url,
  url,
  onIconClick,
}: SiteIconType & { onIconClick?: (url: string) => void }) => {
  return (
    <TouchableOpacity
      style={styles.iconContainer}
      onPress={() => onIconClick?.(url)}
    >
      <Image source={{ uri: icon_url }} style={{ width: 32, height: 32 }} />
    </TouchableOpacity>
  )
}

const styles = StyleSheet.create({
  iconContainer: {
    width: 'auto',
    alignSelf: 'flex-start',
    alignItems: 'baseline',
    padding: 10,
    backgroundColor: '#efefef',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ccc',
  },
})
