import { useTheme } from '@/theme'
import { Ionicons } from '@expo/vector-icons'
import { rgba } from 'polished'
import React from 'react'
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native'

interface ITags {
  onPress?: () => void
  title: string
  isSmall?: boolean
  showIcon?: boolean
}

export const Tag = ({ onPress, title, showIcon, isSmall }: ITags) => {
  const { themeColors } = useTheme()
  return (
    <TouchableOpacity onPress={onPress}>
      <View
        style={[
          styles.selectedStyle,
          {
            backgroundColor: themeColors.inputBg,
            borderColor: rgba(themeColors.text, 0.2),
          },
          isSmall && { paddingVertical: 2 },
          showIcon && { marginTop: 10 },
        ]}
      >
        <Text
          style={[
            styles.textSelectedStyle,
            { color: themeColors.text },
            isSmall && { fontSize: 14 },
          ]}
        >
          {title}
        </Text>
        {showIcon ? (
          <Ionicons name='remove-circle-sharp' size={20} color='red' />
        ) : null}
      </View>
    </TouchableOpacity>
  )
}

const styles = StyleSheet.create({
  selectedStyle: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 14,
    marginTop: 8,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderWidth: 1,

    // elevation: 2,
  },
  textSelectedStyle: {
    marginRight: 5,
    fontSize: 16,
  },
})
