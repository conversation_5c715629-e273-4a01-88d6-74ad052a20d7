import { COLORS, FONTS, getPlatFormConstants } from '@/constants'
import { Flex } from '@/styles'
import { Ionicons } from '@expo/vector-icons'
import React, { ReactNode } from 'react'
import {
  Modal,
  StyleSheet,
  Text,
  Pressable,
  KeyboardAvoidingView,
} from 'react-native'
import { Button } from './Button'
import { useTheme } from '@/theme'

interface ICustomModal {
  showModal: boolean
  onBtnPress?: () => void
  isInfoModal?: boolean
  children?: ReactNode
  setShowModal: React.Dispatch<React.SetStateAction<boolean>>
  modalData?: {
    title: string
    description: string
    btnText?: string
  }
}

export const CustomModal = (props: ICustomModal) => {
  const {
    showModal,
    setShowModal,
    onBtnPress,
    isInfoModal,
    children,
    modalData,
  } = props
  const { themeColors } = useTheme()
  return (
    <Modal
      transparent
      statusBarTranslucent
      // presentationStyle='overFullScreen'
      animationType='fade'
      visible={showModal}
      onRequestClose={() => {
        setShowModal(!showModal)
      }}
      style={styles.container}
    >
      <KeyboardAvoidingView
        behavior={getPlatFormConstants().isIos ? 'padding' : 'height'}
        style={styles.container}
      >
        <Pressable
          style={styles.container}
          onPress={() => {
            setShowModal(false)
          }}
        >
          {isInfoModal ? (
            <Flex
              style={[
                styles.modalContainer,
                { backgroundColor: themeColors.background },
              ]}
            >
              <Flex style={[styles.iconContainer]}>
                <Ionicons
                  name='shield-checkmark-sharp'
                  size={60}
                  color={COLORS.white}
                />
              </Flex>
              <Flex gap={30} style={{ marginTop: 40 }}>
                <Text style={{ ...FONTS.h2, color: COLORS.primary }}>
                  {modalData?.title}
                </Text>

                <Text
                  style={{
                    ...FONTS.bodyRegular,
                    textAlign: 'center',
                    color: themeColors.text,
                  }}
                >
                  {modalData?.description}
                </Text>

                <Button onPress={onBtnPress}>
                  {modalData?.btnText ?? 'Continue'}
                </Button>
              </Flex>
            </Flex>
          ) : (
            <Pressable
              onPress={e => {
                e.stopPropagation()
              }}
              style={{ paddingHorizontal: 16 }}
            >
              {children}
            </Pressable>
          )}
        </Pressable>
      </KeyboardAvoidingView>
    </Modal>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
  },
  iconContainer: {
    width: 150,
    height: 150,
    borderRadius: 100,
    backgroundColor: COLORS.primary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalContainer: {
    // width: '0%',
    borderRadius: 8,
    paddingHorizontal: 8,
    paddingTop: 60,
    paddingBottom: 30,
  },
})
