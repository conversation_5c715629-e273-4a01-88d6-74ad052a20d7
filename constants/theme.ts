import { Dimensions } from 'react-native'

const { height, width } = Dimensions.get('window')

export const COLORS = {
  black: '#000000',
  borderColor: '#484F58',
  dark: '#181A20',
  disabled: '#D8D8D8',
  error: '#F75555',
  info: '#246BFD',
  lightBlack: '#1f222a',
  lightBlue: '#eff2fe',
  lightWhite: '#fafafa',
  primary: '#335EF7',
  secondary: '#FFD300',
  success: '#0ABE75',
  tertiary: '#6C4DDA',
  warning: '#FACC15',
  white: '#FFFFFF',
}

export const SIZES = {
  // Global SIZES
  base: 8,
  font: 14,
  radius: 30,
  radiusLg: 50,
  padding: 8,
  paddingMd: 12,
  paddingLg: 16,

  // FONTS Sizes
  largeTitle: 50,
  h1: 36,
  h2: 22,
  h3: 16,
  h4: 14,
  bodyLg: 30,
  bodyMd: 20,
  bodyRegular: 16,
  bodySm: 14,

  // App Dimensions
  width,
  height,
}

export const FONTS = {
  largeTitle: {
    fontFamily: 'bold',
    fontSize: SIZES.largeTitle,
    lineHeight: 55,
    color: 'black',
  },
  medium: {
    fontFamily: 'medium',
    fontSize: SIZES.bodyRegular,
    lineHeight: 24,
    color: 'black',
  },
  h1: {
    fontFamily: 'bold',
    fontSize: SIZES.h1,
    lineHeight: 36,
    color: 'black',
  },
  h2: {
    fontFamily: 'bold',
    fontSize: SIZES.h2,
    lineHeight: 30,
    color: 'black',
  },
  h3: {
    fontFamily: 'bold',
    fontSize: SIZES.h3,
    lineHeight: 22,
    color: 'black',
  },
  h4: { fontFamily: 'bold', fontSize: SIZES.h4, lineHeight: 20 },
  bodyLg: {
    fontFamily: 'regular',
    fontSize: SIZES.bodyLg,
    lineHeight: 36,
    color: 'black',
  },
  bodyMd: {
    fontFamily: 'regular',
    fontSize: SIZES.bodyMd,
    lineHeight: 30,
    color: 'black',
  },
  bodyRegular: {
    fontFamily: 'regular',
    fontSize: SIZES.bodyRegular,
    lineHeight: 22,
    color: 'black',
  },
  bodySm: {
    fontFamily: 'regular',
    fontSize: SIZES.bodySm,
    lineHeight: 20,
    color: 'black',
  },
  link: {
    fontFamily: 'bold',
    fontSize: SIZES.bodyRegular,
    lineHeight: 20,
    color: COLORS.primary,
  },
}

const appTheme = { COLORS, SIZES, FONTS }

export default appTheme
