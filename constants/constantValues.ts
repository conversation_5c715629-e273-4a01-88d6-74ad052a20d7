import Constants from 'expo-constants'
import { Platform } from 'react-native'

export const StorageKey = {
  isOnBoardingDone: 'isOnBoardingDone',
  isLoggedIn: 'isLoggedIn',
  bookmarks: 'bookmarks',
}

export const PATHS = {
  register: '(auth)/register',
  login: '(auth)/login',
  onboarding: `(onboarding)`,
  forgotPassword: '(auth)/forgotPassword',
  verifyOtp: '(auth)/verifyOtp',
  resetPassword: '(auth)/resetPassword',
  link: 'link',
  tags: 'tags',
  websites: 'websites',

  // tabs
  tabs: '(tabs)',
  profile: 'profile',
  bookmarks: 'bookmarks',
}

export const getPlatFormConstants = () => {
  return {
    isIos: Platform?.OS === 'ios',
    isAndroid: Platform.OS === 'android',
    isWeb: Platform.OS === 'web',
    statusbarHeight: Constants.statusBarHeight || 0,
  }
}
